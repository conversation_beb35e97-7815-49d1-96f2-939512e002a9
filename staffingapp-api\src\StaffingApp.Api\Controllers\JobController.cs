using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StaffingApp.Api.Models.DTOs;
using StaffingApp.Api.Services;
using System.Security.Claims;
using System.Threading.Tasks;

namespace StaffingApp.Api.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class JobsController : ControllerBase
    {
        private readonly StaffingService _staffingService;
        private readonly ILogger<JobsController> _logger;

        public JobsController(StaffingService staffingService, ILogger<JobsController> logger)
        {
            _staffingService = staffingService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<List<JobDTO>>> GetJobs()
        {
            try
            {
                // Get the current user's client ID from the JWT token
                var clientIdClaim = User.FindFirst("ClientId");
                if (clientIdClaim == null || !int.TryParse(clientIdClaim.Value, out var clientId))
                {
                    return Unauthorized("Invalid user token");
                }

                var jobs = await _staffingService.GetJobsAsync(clientId);
                return Ok(jobs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching jobs");
                return StatusCode(500, "An error occurred while fetching jobs");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<JobDTO>> GetJob(int id)
        {
            try
            {
                // Get user information from JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                var clientIdClaim = User.FindFirst("ClientId");
                var roleClaim = User.FindFirst(ClaimTypes.Role);

                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId) ||
                    clientIdClaim == null || !int.TryParse(clientIdClaim.Value, out var clientId) ||
                    roleClaim == null)
                {
                    return Unauthorized("Invalid user token");
                }

                var userRole = roleClaim.Value;
                var job = await _staffingService.GetJobByIdAsync(id, clientId, userId, userRole);
                return Ok(job);
            }
            catch (KeyNotFoundException)
            {
                return NotFound($"Job with ID {id} not found");
            }
            catch (UnauthorizedAccessException)
            {
                return Forbid("You don't have access to this job");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching job {JobId}", id);
                return StatusCode(500, "An error occurred while fetching the job");
            }
        }

        [HttpPost]
        public async Task<ActionResult<JobDTO>> CreateJob(CreateJobDTO createJob)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                var jobDto = await _staffingService.CreateJobAsync(createJob, userId);
                return Ok(jobDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating job");
                return StatusCode(500, "An error occurred while creating the job");
            }
        }

        [HttpGet("all")]
        [Authorize(Roles = "Admin,Supervisor")]
        public async Task<ActionResult<IEnumerable<JobDTO>>> GetAllJobs()
        {
            try
            {
                var jobs = await _staffingService.GetAllJobsAsync();
                return Ok(jobs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all jobs");
                return StatusCode(500, "An error occurred while getting all jobs");
            }
        }

        [HttpGet("assigned")]
        [Authorize(Roles = "Staff")]
        public async Task<ActionResult<IEnumerable<JobDTO>>> GetAssignedJobs()
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                var jobs = await _staffingService.GetAssignedJobsAsync(userId);
                return Ok(jobs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting assigned jobs");
                return StatusCode(500, "An error occurred while getting assigned jobs");
            }
        }

        [HttpPut("{id}/assign")]
        [Authorize(Roles = "Supervisor")]
        public async Task<ActionResult> AssignJob(int id, [FromBody] AssignJobRequest request)
        {
            try
            {
                // Get the current user ID from the JWT token (supervisor)
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var supervisorId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.AssignJobAsync(id, request.StaffId, supervisorId, request.Notes);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning job");
                return StatusCode(500, "An error occurred while assigning the job");
            }
        }

        [HttpPut("{id}/start")]
        [Authorize(Roles = "Staff")]
        public async Task<ActionResult> StartJob(int id)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.StartJobAsync(id, userId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting job");
                return StatusCode(500, "An error occurred while starting the job");
            }
        }

        [HttpPut("{id}/complete")]
        [Authorize(Roles = "Staff")]
        public async Task<ActionResult> CompleteJob(int id)
        {
            try
            {
                // Get the current user ID from the JWT token
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.CompleteJobAsync(id, userId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing job");
                return StatusCode(500, "An error occurred while completing the job");
            }
        }

        [HttpPut("{id}/reject")]
        [Authorize(Roles = "Supervisor")]
        public async Task<ActionResult> RejectJob(int id)
        {
            try
            {
                // Get the current user ID from the JWT token (supervisor)
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var supervisorId))
                {
                    return Unauthorized("Invalid user token");
                }

                await _staffingService.RejectJobAsync(id, supervisorId);
                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting job");
                return StatusCode(500, "An error occurred while rejecting the job");
            }
        }
    }

    public class AssignJobRequest
    {
        public int StaffId { get; set; }
        public string? Notes { get; set; }
    }
}