using StaffingApp.Api.Models;
using StaffingApp.Api.Services;

namespace StaffingApp.Api.Data;

public static class SeedData
{
    public static void Initialize(StaffingContext context)
    {
        // Only seed if the database is empty
        if (context.Clients.Any() || context.Users.Any() || context.Jobs.Any())
            return;

        Console.WriteLine("Database is empty, starting seeding...");
        // Add sample clients
        var individualClient = new Client
        {
            Name = "John Doe",
            ClientType = ClientType.Individual,
            HoursBought = 40.0m
        };

        var corporateClient = new Client
        {
            Name = "Tech Corp Inc.",
            ClientType = ClientType.Corporate,
            HoursBought = 200.0m
        };

        context.Clients.AddRange(individualClient, corporateClient);
        context.SaveChanges();

        // Add sample users with hashed passwords
        var password1 = "password123";
        var password2 = "password456";
        
        var user1 = new User
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Password = PasswordHasher.HashPassword(password1, out string salt1),
            PasswordSalt = salt1,
            ClientId = individualClient.Id
        };

        var user2 = new User
        {

            FirstName = "Jane",
            LastName = "Smith",
            Email = "<EMAIL>",
            Password = PasswordHasher.HashPassword(password2, out string salt2),
            PasswordSalt = salt2,
            ClientId = corporateClient.Id
        };

        // Add admin user
        var adminPassword = "admin123";
        var adminUser = new User
        {
            FirstName = "Admin",
            LastName = "User",
            Email = "<EMAIL>",
            Password = PasswordHasher.HashPassword(adminPassword, out string adminSalt),
            PasswordSalt = adminSalt,
            ClientId = corporateClient.Id,
            Role = "Admin"
        };

        // Add supervisor user
        var supervisorPassword = "supervisor123";
        var supervisorUser = new User
        {
            FirstName = "Sarah",
            LastName = "Supervisor",
            Email = "<EMAIL>",
            Password = PasswordHasher.HashPassword(supervisorPassword, out string supervisorSalt),
            PasswordSalt = supervisorSalt,
            ClientId = corporateClient.Id,
            Role = "Supervisor"
        };

        // Add staff user
        var staffPassword = "staff123";
        var staffUser = new User
        {
            FirstName = "Mike",
            LastName = "Staff",
            Email = "<EMAIL>",
            Password = PasswordHasher.HashPassword(staffPassword, out string staffSalt),
            PasswordSalt = staffSalt,
            ClientId = corporateClient.Id,
            Role = "Staff"
        };

        context.Users.AddRange(user1, user2, adminUser, supervisorUser, staffUser);
        context.SaveChanges();

        // Add payment rate
        var paymentRate = new PaymentRate
        {
            RatePerHour = 25.00m,
            Currency = "USD",
            EffectiveFrom = DateTime.UtcNow.AddDays(-30),
            EffectiveTo = null,
            IsActive = true,
            CreatedAt = DateTime.UtcNow.AddDays(-30),
            CreatedById = user1.Id,
            CreatedBy = user1
        };

        context.PaymentRates.Add(paymentRate);
        context.SaveChanges();

        // Add sample job
        var job = new Job
        {
            Title = "Data Entry Project",
            Description = "Sample data entry project for testing",
            JobType = JobType.DataEntry,
            JobCategory = JobCategory.DataProcessing,
            OutputFormat = OutputFormat.Excel,
            ClientId = corporateClient.Id,
            CreatedById = user2.Id,
            CreatedAt = DateTime.UtcNow
        };

        context.Jobs.Add(job);
        context.SaveChanges();

        // Add sample task
        var task = new JobTask
        {
            Title = "Process Customer Data",
            Description = "Enter customer information from provided forms",
            JobId = job.Id,
            OutputFormat = OutputFormat.Excel,
            HoursSpent = 15.5m,
            CreatedAt = DateTime.UtcNow
        };

        // Add another sample task
        var task2 = new JobTask
        {
            Title = "Data Validation",
            Description = "Validate and clean the processed customer data",
            JobId = job.Id,
            OutputFormat = OutputFormat.Excel,
            HoursSpent = 8.0m,
            CreatedAt = DateTime.UtcNow
        };

        context.JobTasks.AddRange(task, task2);
        context.SaveChanges();
    }
}
