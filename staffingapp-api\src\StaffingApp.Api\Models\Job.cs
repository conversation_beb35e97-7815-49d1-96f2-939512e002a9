namespace StaffingApp.Api.Models;

public class Job
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public JobType JobType { get; set; }
    public JobCategory JobCategory { get; set; }
    public OutputFormat OutputFormat { get; set; }
    public JobStatus Status { get; set; } = JobStatus.Pending;
    public string? ReferenceUrl { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    public string? AttachmentUrl { get; set; } // URL to the attachment, if any
    
    // Navigation properties
    public ICollection<JobTask> Tasks { get; set; } = new List<JobTask>();
    public ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();

    // Created by which client
    public int ClientId { get; set; }
    public Client Client { get; set; } = null!;
    
    // Created by which user of the client
    public int CreatedById { get; set; }
    public User CreatedBy { get; set; } = null!;

    // Assignment fields
    public int? AssignedToId { get; set; }  // Staff member assigned to this job
    public User? AssignedTo { get; set; }   // Navigation property

    public int? ReviewedById { get; set; }  // Supervisor who reviewed/assigned the job
    public User? ReviewedBy { get; set; }   // Navigation property

    public DateTime? AssignedAt { get; set; }  // When the job was assigned
    public DateTime? ReviewedAt { get; set; }  // When the job was reviewed by supervisor

    public string? SupervisorNotes { get; set; }  // Notes from supervisor during review/assignment
}
