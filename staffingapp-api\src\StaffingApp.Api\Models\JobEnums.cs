namespace StaffingApp.Api.Models;

public enum UserRole
{
    User,
    Admin,
    Supervisor,
    Staff
}

public enum JobType
{
    DataEntry,
    Accounting,
    HR,
    ITSupport,
    Marketing,
    Legal,
    CustomerService,
    Other
}

public enum JobCategory
{
    // Data Entry Categories
    DataProcessing,
    DataCleaning,
    DocumentationEntry,
    
    // Accounting Categories
    Bookkeeping,
    FinancialReporting,
    Taxation,
    Payroll,
    
    // HR Categories
    Recruitment,
    EmployeeRelations,
    Training,
    CompensationBenefits,
    
    // IT Support Categories
    TechnicalSupport,
    NetworkSupport,
    SoftwareSupport,
    HardwareSupport,
    
    // Marketing Categories
    DigitalMarketing,
    ContentCreation,
    SocialMedia,
    MarketResearch,
    
    // Legal Categories
    ContractReview,
    Compliance,
    LegalResearch,
    Documentation,
    
    // Customer Service Categories
    CallCenter,
    EmailSupport,
    ChatSupport,
    CustomerFeedback,
    
    // Other
    Other
}

public enum OutputFormat
{
    PDF,
    Word,
    Excel,
    PlainText,
    JSON,
    XML,
    Database,
    Other
}

public enum JobStatus
{
    Pending,           // Submitted by client, waiting for supervisor review
    UnderReview,       // Being reviewed by supervisor
    Assigned,          // Assigned to staff member by supervisor
    InProgress,        // Being worked on by staff
    Completed,         // Completed by staff
    Delivered,         // Delivered to client
    Cancelled,         // Cancelled
    OnHold,           // Put on hold
    Rejected          // Rejected by supervisor
}
