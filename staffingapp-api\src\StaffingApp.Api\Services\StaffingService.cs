using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using StaffingApp.Api.Data;
using StaffingApp.Api.Models;
using StaffingApp.Api.Models.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace StaffingApp.Api.Services
{
    public class StaffingService
    {
        private readonly StaffingContext _context;
        private readonly ILogger<StaffingService> _logger;

        public StaffingService(StaffingContext context, ILogger<StaffingService> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<HoursStatisticsDTO> GetClientHoursStatisticsAsync(int clientId)
        {
            try
            {
                _logger.LogInformation("Fetching hours statistics for client {ClientId}", clientId);

                // Verify client exists with explicit loading
                var client = await _context.Clients
                    .AsNoTracking()
                    .Include(c => c.Jobs)
                        .ThenInclude(j => j.Tasks)
                    .FirstOrDefaultAsync(c => c.Id == clientId);

                if (client == null)
                {
                    _logger.LogWarning("Client not found with ID {ClientId}", clientId);
                    throw new KeyNotFoundException($"Client with ID {clientId} not found");
                }

                // Log raw data for debugging
                _logger.LogDebug("Client data: {@Client}", new { client.Id, client.HoursBought, JobCount = client.Jobs?.Count ?? 0 });

                decimal hoursBought = client.HoursBought;
                decimal hoursUtilized = 0;

                if (client.Jobs != null)
                {
                    foreach (var job in client.Jobs)
                    {
                        if (job.Tasks != null)
                        {
                            foreach (var task in job.Tasks)
                            {
                                hoursUtilized += task.HoursSpent;
                                _logger.LogDebug("Task {TaskId} hours: {Hours}", task.Id, task.HoursSpent);
                            }
                        }
                    }
                }

                var statistics = new HoursStatisticsDTO
                {
                    HoursBought = hoursBought,
                    HoursUtilized = hoursUtilized,
                    HoursAvailable = Math.Max(0, hoursBought - hoursUtilized)
                };

                _logger.LogInformation("Statistics calculated: {@Statistics}", statistics);
                return statistics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating statistics for client {ClientId}", clientId);
                throw;
            }
        }
        public async Task<Client> GetClientAsync(int clientId)
        {
            var client = await _context.Clients
                .Include(c => c.Jobs)
                    .ThenInclude(j => j.Tasks)
                .FirstOrDefaultAsync(c => c.Id == clientId);

            if (client == null)
            {
                _logger.LogWarning("Client not found with ID {ClientId}", clientId);
                throw new KeyNotFoundException($"Client with ID {clientId} not found");
            }

            return client;
        }
        
        public async Task<JobDTO> CreateJobAsync(CreateJobDTO createJob, int createdById)
{
    var job = new Job
    {
        Title = createJob.Title,
        Description = createJob.Description,
        JobType = Enum.TryParse<JobType>(createJob.JobType, out var jobType) ? jobType : throw new ArgumentException("Invalid JobType"),
        JobCategory = Enum.TryParse<JobCategory>(createJob.Category, out var jobCategory) ? jobCategory : throw new ArgumentException("Invalid JobCategory"),
        OutputFormat = Enum.TryParse<OutputFormat>(createJob.OutputFormat, out var outputFormat) ? outputFormat : throw new ArgumentException("Invalid OutputFormat"),
        AttachmentUrl = createJob.AttachmentUrl,
        ReferenceUrl = createJob.ReferenceUrl,
        ClientId = createJob.ClientId,
        CreatedById = createdById,
        CreatedAt = DateTime.UtcNow
    };

    _context.Jobs.Add(job);
    await _context.SaveChangesAsync();

    return new JobDTO
    {
        Id = job.Id,
        Title = job.Title,
        Description = job.Description,
        JobType = job.JobType.ToString(),
        Category = job.JobCategory.ToString(),
        OutputFormat = job.OutputFormat.ToString(),
        Status = job.Status.ToString(),
        AttachmentUrl = job.AttachmentUrl,
        ReferenceUrl = job.ReferenceUrl,
        ClientId = job.ClientId,
        CreatedAt = job.CreatedAt,
        UpdatedAt = job.UpdatedAt
    };
}

        public async Task<JobDTO> GetJobByIdAsync(int jobId, int clientId, int userId, string userRole)
        {
            try
            {
                var job = await _context.Jobs
                    .Include(j => j.Tasks)
                    .Include(j => j.Attachments)
                    .Include(j => j.AssignedTo)
                    .Include(j => j.ReviewedBy)
                    .FirstOrDefaultAsync(j => j.Id == jobId);

                if (job == null)
                {
                    throw new KeyNotFoundException($"Job with ID {jobId} not found");
                }

                // Check access based on user role
                bool hasAccess = false;

                switch (userRole)
                {
                    case "User":
                        // Regular users can only access jobs from their own client
                        hasAccess = job.ClientId == clientId;
                        break;
                    case "Supervisor":
                        // Supervisors can access jobs they've reviewed or any job (for assignment purposes)
                        hasAccess = job.ReviewedById == userId || true; // Allow all jobs for supervisors
                        break;
                    case "Staff":
                        // Staff can access jobs assigned to them or jobs from their client
                        hasAccess = job.AssignedToId == userId || job.ClientId == clientId;
                        break;
                    case "Admin":
                        // Admins can access all jobs
                        hasAccess = true;
                        break;
                    default:
                        hasAccess = false;
                        break;
                }

                if (!hasAccess)
                {
                    throw new UnauthorizedAccessException("You don't have access to this job");
                }

                return new JobDTO
                {
                    Id = job.Id,
                    Title = job.Title,
                    Description = job.Description,
                    JobType = job.JobType.ToString(),
                    Category = job.JobCategory.ToString(),
                    OutputFormat = job.OutputFormat.ToString(),
                    Status = job.Status.ToString(),
                    AttachmentUrl = job.AttachmentUrl,
                    ReferenceUrl = job.ReferenceUrl,
                    ClientId = job.ClientId,
                    CreatedAt = job.CreatedAt,
                    UpdatedAt = job.UpdatedAt,
                    AssignedToId = job.AssignedToId,
                    AssignedTo = job.AssignedTo != null ? new User
                    {
                        Id = job.AssignedTo.Id,
                        FirstName = job.AssignedTo.FirstName,
                        LastName = job.AssignedTo.LastName,
                        Email = job.AssignedTo.Email,
                        Role = job.AssignedTo.Role,
                        ClientId = job.AssignedTo.ClientId
                    } : null,
                    ReviewedById = job.ReviewedById,
                    ReviewedBy = job.ReviewedBy != null ? new User
                    {
                        Id = job.ReviewedBy.Id,
                        FirstName = job.ReviewedBy.FirstName,
                        LastName = job.ReviewedBy.LastName,
                        Email = job.ReviewedBy.Email,
                        Role = job.ReviewedBy.Role,
                        ClientId = job.ReviewedBy.ClientId
                    } : null,
                    AssignedAt = job.AssignedAt,
                    ReviewedAt = job.ReviewedAt,
                    SupervisorNotes = job.SupervisorNotes
                };
            }
            catch (Exception ex) when (!(ex is KeyNotFoundException || ex is UnauthorizedAccessException))
            {
                _logger.LogError(ex, "Error fetching job {JobId} for user {UserId} with role {UserRole}", jobId, userId, userRole);
                throw;
            }
        }

        public async Task<List<JobDTO>> GetAllJobsAsync()
        {
            try
            {
                var jobs = await _context.Jobs
                    .Include(j => j.AssignedTo)
                    .Include(j => j.ReviewedBy)
                    .Include(j => j.CreatedBy)
                    .OrderByDescending(j => j.CreatedAt)
                    .ToListAsync();

                return jobs.Select(job => new JobDTO
                {
                    Id = job.Id,
                    Title = job.Title,
                    Description = job.Description,
                    JobType = job.JobType.ToString(),
                    Category = job.JobCategory.ToString(),
                    OutputFormat = job.OutputFormat.ToString(),
                    Status = job.Status.ToString(),
                    AttachmentUrl = job.AttachmentUrl,
                    ReferenceUrl = job.ReferenceUrl,
                    ClientId = job.ClientId,
                    CreatedAt = job.CreatedAt,
                    UpdatedAt = job.UpdatedAt,
                    AssignedToId = job.AssignedToId,
                    ReviewedById = job.ReviewedById,
                    AssignedAt = job.AssignedAt,
                    ReviewedAt = job.ReviewedAt,
                    SupervisorNotes = job.SupervisorNotes
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching all jobs");
                throw;
            }
        }

        public async Task<List<JobDTO>> GetJobsAsync(int clientId)
        {
            try
            {
                var jobs = await _context.Jobs
                    .Include(j => j.AssignedTo)
                    .Include(j => j.ReviewedBy)
                    .Where(j => j.ClientId == clientId)
                    .OrderByDescending(j => j.CreatedAt)
                    .ToListAsync();

                return jobs.Select(job => new JobDTO
                {
                    Id = job.Id,
                    Title = job.Title,
                    Description = job.Description,
                    JobType = job.JobType.ToString(),
                    Category = job.JobCategory.ToString(),
                    OutputFormat = job.OutputFormat.ToString(),
                    Status = job.Status.ToString(),
                    AttachmentUrl = job.AttachmentUrl,
                    ReferenceUrl = job.ReferenceUrl,
                    ClientId = job.ClientId,
                    CreatedAt = job.CreatedAt,
                    UpdatedAt = job.UpdatedAt,
                    AssignedToId = job.AssignedToId,
                    ReviewedById = job.ReviewedById,
                    AssignedAt = job.AssignedAt,
                    ReviewedAt = job.ReviewedAt,
                    SupervisorNotes = job.SupervisorNotes
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching jobs for client {ClientId}", clientId);
                throw;
            }
        }

        public async Task<AccountStatementDTO> GetClientAccountStatementAsync(int clientId)
        {
            try
            {
                _logger.LogInformation("Getting account statement for client {ClientId}", clientId);

                var client = await _context.Clients
                    .Include(c => c.Users)
                    .Include(c => c.Jobs)
                        .ThenInclude(j => j.Tasks)
                    .FirstOrDefaultAsync(c => c.Id == clientId);

                if (client == null)
                {
                    throw new KeyNotFoundException($"Client with ID {clientId} not found");
                }

                // Get all hours purchases for this client's users
                var userIds = client.Users.Select(u => u.Id).ToList();
                var purchases = await _context.HoursPurchases
                    .Include(p => p.PaymentRate)
                    .Where(p => userIds.Contains(p.UserId) && p.Status == HoursPurchaseStatus.Approved)
                    .OrderBy(p => p.ProcessedAt ?? p.CreatedAt)
                    .ToListAsync();

                // Get all job tasks for this client
                var jobTasks = client.Jobs
                    .SelectMany(j => j.Tasks ?? new List<JobTask>())
                    .OrderBy(t => t.CreatedAt)
                    .ToList();

                var entries = new List<AccountStatementEntryDTO>();
                decimal runningHoursBalance = 0;
                decimal runningAmountBalance = 0;

                // Combine purchases and tasks, then sort by date
                var allTransactions = new List<(DateTime Date, string Type, object Data)>();

                // Add purchases
                foreach (var purchase in purchases)
                {
                    allTransactions.Add((
                        purchase.ProcessedAt ?? purchase.CreatedAt,
                        "Purchase",
                        purchase
                    ));
                }

                // Add job tasks
                foreach (var task in jobTasks)
                {
                    allTransactions.Add((
                        task.CreatedAt,
                        "Usage",
                        task
                    ));
                }

                // Sort all transactions by date (oldest first for balance calculation)
                allTransactions = allTransactions.OrderBy(t => t.Date).ToList();

                // Process each transaction
                foreach (var transaction in allTransactions)
                {
                    var entry = new AccountStatementEntryDTO
                    {
                        Date = transaction.Date,
                        Type = transaction.Type
                    };

                    if (transaction.Type == "Purchase" && transaction.Data is HoursPurchase purchase)
                    {
                        entry.Reference = purchase.PaymentReference;
                        entry.Transaction = $"Hours Purchase - {purchase.Hours} hours";
                        entry.HoursBought = purchase.Hours;
                        entry.Amount = purchase.Amount;

                        runningHoursBalance += purchase.Hours;
                        runningAmountBalance += purchase.Amount;
                    }
                    else if (transaction.Type == "Usage" && transaction.Data is JobTask task)
                    {
                        entry.Reference = $"Job #{task.JobId}";
                        entry.Transaction = $"Task: {task.Title}";
                        entry.HoursSpent = task.HoursSpent;

                        runningHoursBalance -= task.HoursSpent;
                        // For amount calculation, we need the rate at the time of task creation
                        // For simplicity, we'll use the current active rate or a default
                        var currentRate = await _context.PaymentRates
                            .Where(r => r.IsActive)
                            .FirstOrDefaultAsync();
                        var ratePerHour = currentRate?.RatePerHour ?? 25.0m; // Default rate
                        var taskAmount = task.HoursSpent * ratePerHour;
                        entry.Amount = -taskAmount; // Negative because it's a deduction
                        runningAmountBalance -= taskAmount;
                    }

                    entry.BalanceHours = runningHoursBalance;
                    entry.BalanceAmount = runningAmountBalance;
                    entries.Add(entry);
                }

                // Reverse entries to show newest transactions first
                entries.Reverse();

                var statement = new AccountStatementDTO
                {
                    Entries = entries,
                    TotalHoursBought = purchases.Sum(p => p.Hours),
                    TotalHoursSpent = jobTasks.Sum(t => t.HoursSpent),
                    CurrentHoursBalance = runningHoursBalance,
                    TotalAmountSpent = purchases.Sum(p => p.Amount),
                    CurrentAmountBalance = runningAmountBalance
                };

                _logger.LogInformation("Account statement generated for client {ClientId}: {EntryCount} entries",
                    clientId, entries.Count);
                return statement;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account statement for client {ClientId}", clientId);
                throw;
            }
        }

        public async Task<List<JobDTO>> GetAssignedJobsAsync(int staffId)
        {
            try
            {
                var jobs = await _context.Jobs
                    .Include(j => j.AssignedTo)
                    .Include(j => j.ReviewedBy)
                    .Where(j => j.AssignedToId == staffId)
                    .OrderByDescending(j => j.AssignedAt)
                    .ToListAsync();

                return jobs.Select(job => new JobDTO
                {
                    Id = job.Id,
                    Title = job.Title,
                    Description = job.Description,
                    JobType = job.JobType.ToString(),
                    Category = job.JobCategory.ToString(),
                    OutputFormat = job.OutputFormat.ToString(),
                    Status = job.Status.ToString(),
                    AttachmentUrl = job.AttachmentUrl,
                    ReferenceUrl = job.ReferenceUrl,
                    ClientId = job.ClientId,
                    CreatedAt = job.CreatedAt,
                    UpdatedAt = job.UpdatedAt,
                    AssignedAt = job.AssignedAt,
                    SupervisorNotes = job.SupervisorNotes
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching assigned jobs for staff {StaffId}", staffId);
                throw;
            }
        }

        public async Task AssignJobAsync(int jobId, int staffId, int supervisorId, string? notes)
        {
            try
            {
                var job = await _context.Jobs.FindAsync(jobId);
                if (job == null)
                {
                    throw new KeyNotFoundException($"Job with ID {jobId} not found");
                }

                var staff = await _context.Users.FindAsync(staffId);
                if (staff == null || staff.Role != "Staff")
                {
                    throw new ArgumentException("Invalid staff member");
                }

                job.AssignedToId = staffId;
                job.ReviewedById = supervisorId;
                job.AssignedAt = DateTime.UtcNow;
                job.ReviewedAt = DateTime.UtcNow;
                job.SupervisorNotes = notes;
                job.Status = JobStatus.Assigned;
                job.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error assigning job {JobId} to staff {StaffId}", jobId, staffId);
                throw;
            }
        }

        public async Task StartJobAsync(int jobId, int staffId)
        {
            try
            {
                var job = await _context.Jobs.FindAsync(jobId);
                if (job == null)
                {
                    throw new KeyNotFoundException($"Job with ID {jobId} not found");
                }

                if (job.AssignedToId != staffId)
                {
                    throw new UnauthorizedAccessException("You are not assigned to this job");
                }

                if (job.Status != JobStatus.Assigned)
                {
                    throw new InvalidOperationException("Job is not in assigned status");
                }

                job.Status = JobStatus.InProgress;
                job.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting job {JobId} for staff {StaffId}", jobId, staffId);
                throw;
            }
        }

        public async Task CompleteJobAsync(int jobId, int staffId, string outputDetails)
        {
            try
            {
                var job = await _context.Jobs.FindAsync(jobId);
                if (job == null)
                {
                    throw new KeyNotFoundException($"Job with ID {jobId} not found");
                }

                if (job.AssignedToId != staffId)
                {
                    throw new UnauthorizedAccessException("You are not assigned to this job");
                }

                if (job.Status != JobStatus.InProgress)
                {
                    throw new InvalidOperationException("Job is not in progress");
                }

                job.Status = JobStatus.Completed;
                job.OutputDetails = outputDetails;
                job.CompletedAt = DateTime.UtcNow;
                job.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error completing job {JobId} for staff {StaffId}", jobId, staffId);
                throw;
            }
        }

        public async Task RejectJobAsync(int jobId, int supervisorId)
        {
            try
            {
                var job = await _context.Jobs.FindAsync(jobId);
                if (job == null)
                {
                    throw new KeyNotFoundException($"Job with ID {jobId} not found");
                }

                if (job.Status != JobStatus.Pending)
                {
                    throw new InvalidOperationException("Only pending jobs can be rejected");
                }

                job.Status = JobStatus.Rejected;
                job.ReviewedById = supervisorId;
                job.ReviewedAt = DateTime.UtcNow;
                job.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting job {JobId} by supervisor {SupervisorId}", jobId, supervisorId);
                throw;
            }
        }

        public async Task ReviewCompletedJobAsync(int jobId, int supervisorId)
        {
            try
            {
                var job = await _context.Jobs.FindAsync(jobId);
                if (job == null)
                {
                    throw new KeyNotFoundException($"Job with ID {jobId} not found");
                }

                if (job.Status != JobStatus.Completed)
                {
                    throw new InvalidOperationException("Only completed jobs can be reviewed");
                }

                job.Status = JobStatus.Delivered;
                job.DeliveredAt = DateTime.UtcNow;
                job.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reviewing completed job {JobId} by supervisor {SupervisorId}", jobId, supervisorId);
                throw;
            }
        }

        public async Task MarkJobSatisfiedAsync(int jobId, int clientUserId)
        {
            try
            {
                var job = await _context.Jobs
                    .Include(j => j.Client)
                    .FirstOrDefaultAsync(j => j.Id == jobId);

                if (job == null)
                {
                    throw new KeyNotFoundException($"Job with ID {jobId} not found");
                }

                // Verify the user belongs to the client that created the job
                var user = await _context.Users.FindAsync(clientUserId);
                if (user == null || user.ClientId != job.ClientId)
                {
                    throw new UnauthorizedAccessException("You don't have access to this job");
                }

                if (job.Status != JobStatus.Delivered)
                {
                    throw new InvalidOperationException("Only delivered jobs can be marked as satisfied");
                }

                job.Status = JobStatus.Satisfied;
                job.SatisfiedAt = DateTime.UtcNow;
                job.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking job {JobId} as satisfied by client user {ClientUserId}", jobId, clientUserId);
                throw;
            }
        }

        public async Task<Job?> GetJobByIdForDebugAsync(int jobId)
        {
            return await _context.Jobs
                .FirstOrDefaultAsync(j => j.Id == jobId);
        }

        public async Task<List<Job>> GetAllJobsForDebugAsync()
        {
            return await _context.Jobs.ToListAsync();
        }

        public async Task<List<Client>> GetAllClientsForDebugAsync()
        {
            return await _context.Clients.ToListAsync();
        }

        public async Task<int> CreateJobForIndividualClientsAsync()
        {
            // Find all individual clients
            var individualClients = await _context.Clients
                .Where(c => c.ClientType == ClientType.Individual)
                .ToListAsync();

            int jobsCreated = 0;

            foreach (var client in individualClients)
            {
                // Check if this client already has jobs
                var existingJobs = await _context.Jobs
                    .Where(j => j.ClientId == client.Id)
                    .CountAsync();

                if (existingJobs == 0)
                {
                    // Find a user for this client to be the creator
                    var user = await _context.Users
                        .FirstOrDefaultAsync(u => u.ClientId == client.Id);

                    if (user != null)
                    {
                        var job = new Job
                        {
                            Title = "Personal Document Processing",
                            Description = "Process and organize personal documents for " + client.Name,
                            JobType = JobType.DataEntry,
                            JobCategory = JobCategory.DocumentationEntry,
                            OutputFormat = OutputFormat.PDF,
                            ClientId = client.Id,
                            CreatedById = user.Id,
                            CreatedAt = DateTime.UtcNow,
                            Status = JobStatus.Submitted
                        };

                        _context.Jobs.Add(job);
                        jobsCreated++;
                    }
                }
            }

            if (jobsCreated > 0)
            {
                await _context.SaveChangesAsync();
            }

            return jobsCreated;
        }
    }
}