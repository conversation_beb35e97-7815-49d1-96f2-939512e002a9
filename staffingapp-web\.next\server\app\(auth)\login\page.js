(()=>{var e={};e.id=665,e.ids=[665],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},6087:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(3272),s(5795),s(5866),s(2029);var r=s(3191),a=s(8716),o=s(7922),n=s.n(o),i=s(5231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["(auth)",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3272)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\(auth)\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5795)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\(auth)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\(auth)\\login\\page.tsx"],u="/(auth)/login/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(auth)/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6938:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},7286:()=>{},1547:(e,t,s)=>{Promise.resolve().then(s.bind(s,1439))},5303:()=>{},1439:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(326),a=s(7577),o=s(434),n=s(5047);function i(){(0,n.useRouter)();let[e,t]=(0,a.useState)({email:"",password:""}),[s,i]=(0,a.useState)(!1),[l,d]=(0,a.useState)(null),c=async t=>{t.preventDefault(),i(!0),d(null),console.log("Login attempt with:",e);try{console.log("Sending request to API...");let t=await fetch("http://localhost:5000/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(console.log("Response status:",t.status),console.log("Response ok:",t.ok),!t.ok){let e=await t.text();throw console.log("Error response:",e),Error(`Login failed: ${t.status}`)}let s=await t.json();console.log("Login successful:",s),sessionStorage.setItem("token",s.token),sessionStorage.setItem("user",JSON.stringify(s.user)),document.cookie=`token=${s.token}; path=/; max-age=86400; samesite=lax`;let r=s.user.role,a="/dashboard";"Admin"===r?a="/admin":"Supervisor"===r?a="/supervisor":"Staff"===r&&(a="/staff"),console.log("Redirecting to:",a,"for role:",r),window.location.href=a}catch(e){console.error("Login error:",e),d(`Login failed: ${e instanceof Error?e.message:"Unknown error"}`)}finally{i(!1)}};return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md",children:[r.jsx("h2",{className:"text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),l&&r.jsx("div",{className:"bg-red-50 p-4 rounded-md",children:r.jsx("p",{className:"text-sm text-red-700",children:l})}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:c,children:[(0,r.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),r.jsx("input",{id:"email",name:"email",type:"email",required:!0,value:e.email,onChange:s=>t({...e,email:s.target.value}),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm",placeholder:"Email address"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),r.jsx("input",{id:"password",name:"password",type:"password",required:!0,value:e.password,onChange:s=>t({...e,password:s.target.value}),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm",placeholder:"Password"})]})]}),r.jsx("button",{type:"submit",disabled:s,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50",children:s?"Signing in...":"Sign in"})]}),r.jsx("div",{className:"text-center",children:r.jsx(o.default,{href:"/signup",className:"text-sm text-emerald-600 hover:text-emerald-500",children:"Don't have an account? Sign up"})})]})})}},5795:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(9510);function a({children:e}){return r.jsx(r.Fragment,{children:e})}},3272:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\(auth)\login\page.tsx#default`)},2029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,metadata:()=>n});var r=s(9510),a=s(5384),o=s.n(a);s(5023);let n={title:"Staff Hall",description:"A modern staffing solution"};function i({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:o().className,children:e})})}},3881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(6621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[948,349,621,496],()=>s(6087));module.exports=r})();