(()=>{var e={};e.id=130,e.ids=[130],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},3492:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>p}),s(5480),s(2029),s(5866);var r=s(3191),a=s(8716),n=s(7922),i=s.n(n),o=s(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let p=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5480)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\page.tsx"],c="/(dashboard)/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/(dashboard)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},6938:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},7286:()=>{},1391:(e,t,s)=>{Promise.resolve().then(s.bind(s,4497))},4497:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(326),a=s(434);s(7577);var n=s(5047);function i(){return(0,n.useRouter)(),r.jsx("main",{className:"min-h-screen flex flex-col items-center justify-center",children:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("h1",{className:"text-4xl font-bold text-center",children:"Welcome to Staffing App"}),(0,r.jsxs)("div",{className:"flex gap-4 justify-center",children:[r.jsx(a.default,{href:"/login",className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Login"}),r.jsx(a.default,{href:"/signup",className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:"Sign Up"})]})]})})}},2029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>i});var r=s(9510),a=s(5384),n=s.n(a);s(5023);let i={title:"Staff Hall",description:"A modern staffing solution"};function o({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:n().className,children:e})})}},5480:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\page.tsx#default`)},3881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(6621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[948,349,621,496],()=>s(3492));module.exports=r})();