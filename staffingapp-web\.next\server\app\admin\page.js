(()=>{var e={};e.id=3,e.ids=[3],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},5408:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>c}),s(4753),s(2029),s(5866);var a=s(3191),r=s(8716),i=s(7922),n=s.n(i),l=s(5231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4753)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\admin\\page.tsx"],m="/admin/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6938:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},7286:()=>{},6291:(e,t,s)=>{Promise.resolve().then(s.bind(s,4189))},5047:(e,t,s)=>{"use strict";var a=s(7389);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},4189:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(326),r=s(7577),i=s(5047),n=s(2630);let l=e=>e.replace(/([A-Z])/g," $1").trim(),d={[n.O4.DataEntry]:[n.WZ.DataProcessing,n.WZ.DataCleaning,n.WZ.DocumentationEntry],[n.O4.Accounting]:[n.WZ.Bookkeeping,n.WZ.FinancialReporting,n.WZ.Taxation,n.WZ.Payroll],[n.O4.HR]:[n.WZ.Recruitment,n.WZ.EmployeeRelations,n.WZ.Training,n.WZ.CompensationBenefits],[n.O4.ITSupport]:[n.WZ.TechnicalSupport,n.WZ.NetworkSupport,n.WZ.SoftwareSupport,n.WZ.HardwareSupport],[n.O4.Marketing]:[n.WZ.DigitalMarketing,n.WZ.ContentCreation,n.WZ.SocialMedia,n.WZ.MarketResearch],[n.O4.Legal]:[n.WZ.ContractReview,n.WZ.Compliance,n.WZ.LegalResearch,n.WZ.Documentation],[n.O4.CustomerService]:[n.WZ.CallCenter,n.WZ.EmailSupport,n.WZ.ChatSupport,n.WZ.CustomerFeedback],[n.O4.Other]:[n.WZ.Other]},c=e=>{for(let[t,s]of Object.entries(d))if(s.includes(e))return t;return n.O4.Other};function o(){(0,i.useRouter)();let[e,t]=(0,r.useState)(!0),[s,n]=(0,r.useState)("overview"),[l,d]=(0,r.useState)(null);return e?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[a.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex justify-between h-16",children:a.jsx("div",{className:"flex",children:a.jsx("div",{className:"flex-shrink-0 flex items-center",children:a.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall Admin"})})})})})}),a.jsx("main",{className:"pt-24 py-10",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-600",children:"Loading admin dashboard..."})]})})})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[a.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[a.jsx("div",{className:"flex",children:a.jsx("div",{className:"flex-shrink-0 flex items-center",children:a.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall Admin"})})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Welcome, ",l?.firstName]}),a.jsx("a",{href:"/dashboard",className:"text-emerald-600 hover:text-emerald-800 text-sm font-medium",children:"User Dashboard"}),a.jsx("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:a.jsx("span",{children:"Sign Out"})})]})]})})}),a.jsx("main",{className:"pt-24 py-10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),a.jsx("p",{className:"mt-2 text-gray-600",children:"Manage your Staff Hall application settings and configurations"})]}),a.jsx("div",{className:"mb-8",children:a.jsx("div",{className:"border-b border-gray-200",children:a.jsx("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",name:"Overview",icon:"\uD83D\uDCCA"},{id:"job-types",name:"Job Types",icon:"\uD83D\uDCDD"},{id:"categories",name:"Categories",icon:"\uD83D\uDCC2"},{id:"rates",name:"Hour Rates",icon:"\uD83D\uDCB0"},{id:"bonuses",name:"Bonus Limits",icon:"\uD83C\uDF81"},{id:"users",name:"User Management",icon:"\uD83D\uDC65"},{id:"settings",name:"Settings",icon:"⚙️"}].map(e=>(0,a.jsxs)("button",{onClick:()=>n(e.id),className:`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${s===e.id?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[a.jsx("span",{className:"mr-2",children:e.icon}),e.name]},e.id))})})}),a.jsx("div",{className:"bg-white shadow rounded-lg",children:(()=>{switch(s){case"overview":default:return a.jsx(m,{});case"job-types":return a.jsx(x,{});case"categories":return a.jsx(u,{});case"rates":return a.jsx(p,{});case"bonuses":return a.jsx(h,{});case"users":return a.jsx(g,{});case"settings":return a.jsx(b,{})}})()})]})})]})}function m(){return(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"System Overview"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[a.jsx("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:a.jsx("span",{className:"text-white text-xl",children:"\uD83D\uDC65"})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Total Users"}),a.jsx("div",{className:"text-3xl font-bold text-emerald-900",children:"156"})]})]})}),a.jsx("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:a.jsx("span",{className:"text-white text-xl",children:"\uD83D\uDCDD"})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Active Jobs"}),a.jsx("div",{className:"text-3xl font-bold text-emerald-900",children:"42"})]})]})}),a.jsx("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:a.jsx("span",{className:"text-white text-xl",children:"\uD83D\uDCB0"})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Revenue"}),a.jsx("div",{className:"text-3xl font-bold text-emerald-900",children:"$12,450"})]})]})}),a.jsx("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:a.jsx("span",{className:"text-white text-xl",children:"⏱️"})}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Hours Sold"}),a.jsx("div",{className:"text-3xl font-bold text-emerald-900",children:"2,340"})]})]})})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Activity"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg",children:[a.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:a.jsx("span",{className:"text-white text-sm",children:"\uD83D\uDC64"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:"New user registered"}),a.jsx("p",{className:"text-xs text-gray-500",children:"John Doe joined as Individual account"})]}),a.jsx("span",{className:"text-xs text-gray-400",children:"2 minutes ago"})]}),(0,a.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg",children:[a.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:a.jsx("span",{className:"text-white text-sm",children:"\uD83D\uDCDD"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Job completed"}),a.jsx("p",{className:"text-xs text-gray-500",children:"Data Entry task finished by ABC Corp"})]}),a.jsx("span",{className:"text-xs text-gray-400",children:"15 minutes ago"})]}),(0,a.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg",children:[a.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:a.jsx("span",{className:"text-white text-sm",children:"\uD83D\uDCB0"})}),(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Hours purchased"}),a.jsx("p",{className:"text-xs text-gray-500",children:"Jane Smith bought 50 hours"})]}),a.jsx("span",{className:"text-xs text-gray-400",children:"1 hour ago"})]})]})]})]})}function x(){let[e,t]=(0,r.useState)(()=>Object.values(n.O4).map((e,t)=>({id:t+1,name:e,description:`${l(e)} tasks and services`,isActive:!0}))),[s,i]=(0,r.useState)(!1),[d,c]=(0,r.useState)(null),[o,m]=(0,r.useState)({name:n.O4.DataEntry,description:"",isActive:!0}),x=e=>{c(e),m({name:e.name,description:e.description,isActive:e.isActive}),i(!0)},u=s=>{confirm("Are you sure you want to delete this job type?")&&t(e.filter(e=>e.id!==s))},p=s=>{t(e.map(e=>e.id===s?{...e,isActive:!e.isActive}:e))};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Job Types Management"}),a.jsx("button",{onClick:()=>{i(!0),c(null),m({name:n.O4.DataEntry,description:"",isActive:!0})},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Add Job Type"})]}),s&&(0,a.jsxs)("div",{className:"mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4",children:[a.jsx("h3",{className:"text-md font-medium text-emerald-800 mb-4",children:d?"Edit Job Type":"Add New Job Type"}),(0,a.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),d)t(e.map(e=>e.id===d.id?{...e,...o}:e)),c(null);else{let s={id:Math.max(...e.map(e=>e.id))+1,...o};t([...e,s])}m({name:n.O4.DataEntry,description:"",isActive:!0}),i(!1)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Job Type Name"}),(0,a.jsxs)("select",{required:!0,value:o.name,onChange:e=>m({...o,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:n.O4.DataEntry,children:"Select a job type..."}),Object.values(n.O4).map(e=>a.jsx("option",{value:e,children:l(e)},e))]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:o.isActive.toString(),onChange:e=>m({...o,isActive:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:"true",children:"Active"}),a.jsx("option",{value:"false",children:"Inactive"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),a.jsx("textarea",{required:!0,value:o.description,onChange:e=>m({...o,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",rows:3,placeholder:"Describe this job type..."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{type:"submit",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:[d?"Update":"Add"," Job Type"]}),a.jsx("button",{type:"button",onClick:()=>{i(!1),c(null),m({name:n.O4.DataEntry,description:"",isActive:!0})},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]})]}),a.jsx("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:l(e.name)}),a.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.description}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("button",{onClick:()=>p(e.id),className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-emerald-100 text-emerald-800":"bg-gray-100 text-gray-800"}`,children:e.isActive?"Active":"Inactive"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[a.jsx("button",{onClick:()=>x(e),className:"text-emerald-600 hover:text-emerald-900",children:"Edit"}),a.jsx("button",{onClick:()=>u(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})})]})}function u(){let[e,t]=(0,r.useState)(()=>Object.values(n.WZ).map((e,t)=>({id:t+1,name:e,jobType:c(e),description:`${l(e)} tasks and services`,isActive:!0}))),[s,i]=(0,r.useState)(!1),[d,o]=(0,r.useState)(null),[m,x]=(0,r.useState)({name:n.WZ.DataProcessing,jobType:n.O4.DataEntry,description:"",isActive:!0}),u=e=>{o(e),x({name:e.name,jobType:e.jobType,description:e.description,isActive:e.isActive}),i(!0)},p=s=>{confirm("Are you sure you want to delete this category?")&&t(e.filter(e=>e.id!==s))},h=s=>{t(e.map(e=>e.id===s?{...e,isActive:!e.isActive}:e))};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Categories Management"}),a.jsx("button",{onClick:()=>{i(!0),o(null),x({name:n.WZ.DataProcessing,jobType:n.O4.DataEntry,description:"",isActive:!0})},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Add Category"})]}),s&&(0,a.jsxs)("div",{className:"mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4",children:[a.jsx("h3",{className:"text-md font-medium text-emerald-800 mb-4",children:d?"Edit Category":"Add New Category"}),(0,a.jsxs)("form",{onSubmit:s=>{if(s.preventDefault(),d)t(e.map(e=>e.id===d.id?{...e,...m}:e)),o(null);else{let s={id:Math.max(...e.map(e=>e.id))+1,...m};t([...e,s])}x({name:n.WZ.DataProcessing,jobType:n.O4.DataEntry,description:"",isActive:!0}),i(!1)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category Name"}),(0,a.jsxs)("select",{required:!0,value:m.name,onChange:e=>x({...m,name:e.target.value,jobType:c(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:n.WZ.DataProcessing,children:"Select a category..."}),Object.values(n.WZ).map(e=>a.jsx("option",{value:e,children:l(e)},e))]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Job Type"}),a.jsx("input",{type:"text",value:l(m.jobType),readOnly:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600",placeholder:"Auto-assigned based on category"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:m.isActive.toString(),onChange:e=>x({...m,isActive:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:"true",children:"Active"}),a.jsx("option",{value:"false",children:"Inactive"})]})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),a.jsx("textarea",{required:!0,value:m.description,onChange:e=>x({...m,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",rows:3,placeholder:"Describe this category..."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{type:"submit",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:[d?"Update":"Add"," Category"]}),a.jsx("button",{type:"button",onClick:()=>{i(!1),o(null),x({name:n.WZ.DataProcessing,jobType:n.O4.DataEntry,description:"",isActive:!0})},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]})]}),a.jsx("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category Name"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Type"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:l(e.name)}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-600",children:a.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800",children:l(e.jobType)})}),a.jsx("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.description}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("button",{onClick:()=>h(e.id),className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-emerald-100 text-emerald-800":"bg-gray-100 text-gray-800"}`,children:e.isActive?"Active":"Inactive"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[a.jsx("button",{onClick:()=>u(e),className:"text-emerald-600 hover:text-emerald-900",children:"Edit"}),a.jsx("button",{onClick:()=>p(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})})]})}function p(){let[e,t]=(0,r.useState)([{id:1,packageName:"Basic Package",hours:10,price:50,pricePerHour:5,isActive:!0},{id:2,packageName:"Standard Package",hours:25,price:100,pricePerHour:4,isActive:!0},{id:3,packageName:"Premium Package",hours:50,price:180,pricePerHour:3.6,isActive:!0},{id:4,packageName:"Enterprise Package",hours:100,price:320,pricePerHour:3.2,isActive:!0}]),[s,i]=(0,r.useState)(!1),[n,l]=(0,r.useState)(null),[d,c]=(0,r.useState)({packageName:"",hours:0,price:0,isActive:!0}),o=(e,t)=>t>0?(e/t).toFixed(2):"0.00",m=e=>{l(e),c({packageName:e.packageName,hours:e.hours,price:e.price,isActive:e.isActive}),i(!0)},x=s=>{confirm("Are you sure you want to delete this rate package?")&&t(e.filter(e=>e.id!==s))},u=s=>{t(e.map(e=>e.id===s?{...e,isActive:!e.isActive}:e))};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900",children:"Hour Rates Management"}),a.jsx("button",{onClick:()=>{i(!0),l(null),c({packageName:"",hours:0,price:0,isActive:!0})},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Add Rate Package"})]}),s&&(0,a.jsxs)("div",{className:"mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4",children:[a.jsx("h3",{className:"text-md font-medium text-emerald-800 mb-4",children:n?"Edit Rate Package":"Add New Rate Package"}),(0,a.jsxs)("form",{onSubmit:s=>{s.preventDefault();let a=parseFloat(o(d.price,d.hours));if(n)t(e.map(e=>e.id===n.id?{...e,...d,pricePerHour:a}:e)),l(null);else{let s={id:Math.max(...e.map(e=>e.id))+1,...d,pricePerHour:a};t([...e,s])}c({packageName:"",hours:0,price:0,isActive:!0}),i(!1)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Package Name"}),a.jsx("input",{type:"text",required:!0,value:d.packageName,onChange:e=>c({...d,packageName:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",placeholder:"e.g., Basic Package"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Hours"}),a.jsx("input",{type:"number",required:!0,min:"1",value:d.hours||"",onChange:e=>c({...d,hours:parseInt(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",placeholder:"10"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price ($)"}),a.jsx("input",{type:"number",required:!0,min:"0",step:"0.01",value:d.price||"",onChange:e=>c({...d,price:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",placeholder:"50.00"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:d.isActive.toString(),onChange:e=>c({...d,isActive:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:"true",children:"Active"}),a.jsx("option",{value:"false",children:"Inactive"})]})]})]}),d.hours>0&&d.price>0&&a.jsx("div",{className:"bg-gray-50 p-3 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Price per hour: ",(0,a.jsxs)("span",{className:"font-medium",children:["$",o(d.price,d.hours)]})]})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{type:"submit",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:[n?"Update":"Add"," Rate Package"]}),a.jsx("button",{type:"button",onClick:()=>{i(!1),l(null),c({packageName:"",hours:0,price:0,isActive:!0})},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]})]}),a.jsx("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Package Name"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Hours"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price/Hour"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.packageName}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.hours}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["$",e.price.toFixed(2)]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["$",e.pricePerHour.toFixed(2)]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("button",{onClick:()=>u(e.id),className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-emerald-100 text-emerald-800":"bg-gray-100 text-gray-800"}`,children:e.isActive?"Active":"Inactive"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[a.jsx("button",{onClick:()=>m(e),className:"text-emerald-600 hover:text-emerald-900",children:"Edit"}),a.jsx("button",{onClick:()=>x(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})})]})}function h(){let[e,t]=(0,r.useState)({individual:{enabled:!0,minPurchase:50,bonusPercentage:10,maxBonusAmount:20,description:"Get 10% bonus hours on purchases over $50"},corporate:{enabled:!0,minPurchase:200,bonusPercentage:15,maxBonusAmount:100,description:"Get 15% bonus hours on purchases over $200"}}),[s,i]=(0,r.useState)(null),[n,l]=(0,r.useState)({enabled:!0,minPurchase:0,bonusPercentage:0,maxBonusAmount:0,description:""}),d=t=>{i(t),l(e[t])},c=()=>{s&&(t({...e,[s]:n}),i(null))},o=()=>{i(null),l({enabled:!0,minPurchase:0,bonusPercentage:0,maxBonusAmount:0,description:""})};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-2",children:"Bonus Limits Management"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Configure bonus hour settings for individual and corporate accounts"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-emerald-50 border border-emerald-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-emerald-800",children:"Individual Account Bonuses"}),a.jsx("p",{className:"text-sm text-emerald-600 mt-1",children:"Bonus settings for individual user accounts"})]}),a.jsx("button",{onClick:()=>d("individual"),className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Edit Settings"})]}),"individual"===s?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:n.enabled.toString(),onChange:e=>l({...n,enabled:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:"true",children:"Enabled"}),a.jsx("option",{value:"false",children:"Disabled"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Purchase ($)"}),a.jsx("input",{type:"number",min:"0",step:"0.01",value:n.minPurchase,onChange:e=>l({...n,minPurchase:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bonus Percentage (%)"}),a.jsx("input",{type:"number",min:"0",max:"100",value:n.bonusPercentage,onChange:e=>l({...n,bonusPercentage:parseInt(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Bonus ($)"}),a.jsx("input",{type:"number",min:"0",step:"0.01",value:n.maxBonusAmount,onChange:e=>l({...n,maxBonusAmount:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),a.jsx("textarea",{value:n.description,onChange:e=>l({...n,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",rows:2,placeholder:"Describe the bonus offer..."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("button",{onClick:c,className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Save Changes"}),a.jsx("button",{onClick:o,className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Status:"}),a.jsx("p",{className:"text-sm text-gray-900",children:e.individual.enabled?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Min Purchase:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:["$",e.individual.minPurchase]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Bonus Percentage:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:[e.individual.bonusPercentage,"%"]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Max Bonus:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:["$",e.individual.maxBonusAmount]})]}),(0,a.jsxs)("div",{className:"md:col-span-2 lg:col-span-4",children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Description:"}),a.jsx("p",{className:"text-sm text-gray-900",children:e.individual.description})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-blue-800",children:"Corporate Account Bonuses"}),a.jsx("p",{className:"text-sm text-blue-600 mt-1",children:"Bonus settings for corporate user accounts"})]}),a.jsx("button",{onClick:()=>d("corporate"),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Edit Settings"})]}),"corporate"===s?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:n.enabled.toString(),onChange:e=>l({...n,enabled:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[a.jsx("option",{value:"true",children:"Enabled"}),a.jsx("option",{value:"false",children:"Disabled"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Purchase ($)"}),a.jsx("input",{type:"number",min:"0",step:"0.01",value:n.minPurchase,onChange:e=>l({...n,minPurchase:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bonus Percentage (%)"}),a.jsx("input",{type:"number",min:"0",max:"100",value:n.bonusPercentage,onChange:e=>l({...n,bonusPercentage:parseInt(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Bonus ($)"}),a.jsx("input",{type:"number",min:"0",step:"0.01",value:n.maxBonusAmount,onChange:e=>l({...n,maxBonusAmount:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),a.jsx("textarea",{value:n.description,onChange:e=>l({...n,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",rows:2,placeholder:"Describe the bonus offer..."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[a.jsx("button",{onClick:c,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Save Changes"}),a.jsx("button",{onClick:o,className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Status:"}),a.jsx("p",{className:"text-sm text-gray-900",children:e.corporate.enabled?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Min Purchase:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:["$",e.corporate.minPurchase]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Bonus Percentage:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:[e.corporate.bonusPercentage,"%"]})]}),(0,a.jsxs)("div",{children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Max Bonus:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:["$",e.corporate.maxBonusAmount]})]}),(0,a.jsxs)("div",{className:"md:col-span-2 lg:col-span-4",children:[a.jsx("span",{className:"text-sm font-medium text-gray-500",children:"Description:"}),a.jsx("p",{className:"text-sm text-gray-900",children:e.corporate.description})]})]})]})]})]})}function g(){let[e,t]=(0,r.useState)([{id:1,firstName:"John",lastName:"Doe",email:"<EMAIL>",clientType:"Individual",companyName:null,role:"user",isActive:!0,createdAt:"2024-01-15",hoursBought:50,hoursUsed:25},{id:2,firstName:"Jane",lastName:"Smith",email:"<EMAIL>",clientType:"Corporate",companyName:"ABC Corp",role:"user",isActive:!0,createdAt:"2024-01-10",hoursBought:200,hoursUsed:150},{id:3,firstName:"Admin",lastName:"User",email:"<EMAIL>",clientType:"Individual",companyName:null,role:"admin",isActive:!0,createdAt:"2024-01-01",hoursBought:0,hoursUsed:0}]),[s,i]=(0,r.useState)(""),[n,l]=(0,r.useState)("all"),[d,c]=(0,r.useState)("all"),o=e.filter(e=>{let t=e.firstName.toLowerCase().includes(s.toLowerCase())||e.lastName.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase())||e.companyName&&e.companyName.toLowerCase().includes(s.toLowerCase()),a="all"===n||e.clientType.toLowerCase()===n.toLowerCase(),r="all"===d||e.role===d;return t&&a&&r}),m=s=>{confirm("Are you sure you want to change this user's status?")&&t(e.map(e=>e.id===s?{...e,isActive:!e.isActive}:e))},x=(s,a)=>{confirm(`Are you sure you want to change this user's role to ${a}?`)&&t(e.map(e=>e.id===s?{...e,role:a}:e))};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"User Management"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Users"}),a.jsx("input",{type:"text",value:s,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",placeholder:"Search by name, email, or company..."})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Account Type"}),(0,a.jsxs)("select",{value:n,onChange:e=>l(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:"all",children:"All Types"}),a.jsx("option",{value:"individual",children:"Individual"}),a.jsx("option",{value:"corporate",children:"Corporate"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),(0,a.jsxs)("select",{value:d,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:"all",children:"All Roles"}),a.jsx("option",{value:"user",children:"User"}),a.jsx("option",{value:"admin",children:"Admin"})]})]}),a.jsx("div",{className:"flex items-end",children:(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",o.length," of ",e.length," users"]})})]})]}),a.jsx("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Type"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Hours"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map(e=>(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),a.jsx("div",{className:"text-sm text-gray-500",children:e.email}),e.companyName&&a.jsx("div",{className:"text-sm text-gray-500",children:e.companyName})]})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"Corporate"===e.clientType?"bg-blue-100 text-blue-800":"bg-emerald-100 text-emerald-800"}`,children:e.clientType})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("select",{value:e.role,onChange:t=>x(e.id,t.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[a.jsx("option",{value:"user",children:"User"}),a.jsx("option",{value:"admin",children:"Admin"})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[(0,a.jsxs)("div",{children:["Bought: ",e.hoursBought]}),(0,a.jsxs)("div",{children:["Used: ",e.hoursUsed]}),(0,a.jsxs)("div",{children:["Available: ",e.hoursBought-e.hoursUsed]})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("button",{onClick:()=>m(e.id),className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.isActive?"bg-emerald-100 text-emerald-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Active":"Inactive"})}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[a.jsx("button",{className:"text-emerald-600 hover:text-emerald-900",children:"View Details"}),a.jsx("button",{className:"text-blue-600 hover:text-blue-900",children:"Edit Hours"})]})]},e.id))})]})}),0===o.length&&a.jsx("div",{className:"text-center py-8",children:a.jsx("p",{className:"text-gray-500",children:"No users found matching your criteria."})})]})}function b(){let[e,t]=(0,r.useState)({general:{appName:"Staff Hall",supportEmail:"<EMAIL>",maintenanceMode:!1,allowRegistration:!0},notifications:{emailNotifications:!0,jobCompletionNotifications:!0,paymentNotifications:!0,systemAlerts:!0},security:{sessionTimeout:30,passwordMinLength:8,requireTwoFactor:!1,maxLoginAttempts:5}}),[s,i]=(0,r.useState)("general"),n=(s,a,r)=>{t({...e,[s]:{...e[s],[a]:r}})};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[a.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-2",children:"System Settings"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Configure application-wide settings and preferences"})]}),a.jsx("div",{className:"mb-6",children:a.jsx("nav",{className:"flex space-x-8",children:[{id:"general",name:"General",icon:"⚙️"},{id:"notifications",name:"Notifications",icon:"\uD83D\uDD14"},{id:"security",name:"Security",icon:"\uD83D\uDD12"}].map(e=>(0,a.jsxs)("button",{onClick:()=>i(e.id),className:`flex items-center px-3 py-2 text-sm font-medium rounded-md ${s===e.id?"bg-emerald-100 text-emerald-700":"text-gray-500 hover:text-gray-700"}`,children:[a.jsx("span",{className:"mr-2",children:e.icon}),e.name]},e.id))})}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:["general"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"General Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Application Name"}),a.jsx("input",{type:"text",value:e.general.appName,onChange:e=>n("general","appName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Support Email"}),a.jsx("input",{type:"email",value:e.general.supportEmail,onChange:e=>n("general","supportEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"maintenanceMode",checked:e.general.maintenanceMode,onChange:e=>n("general","maintenanceMode",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"maintenanceMode",className:"ml-2 block text-sm text-gray-900",children:"Enable Maintenance Mode"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"allowRegistration",checked:e.general.allowRegistration,onChange:e=>n("general","allowRegistration",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"allowRegistration",className:"ml-2 block text-sm text-gray-900",children:"Allow New User Registration"})]})]})]}),"notifications"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Notification Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"emailNotifications",checked:e.notifications.emailNotifications,onChange:e=>n("notifications","emailNotifications",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"emailNotifications",className:"ml-2 block text-sm text-gray-900",children:"Enable Email Notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"jobCompletionNotifications",checked:e.notifications.jobCompletionNotifications,onChange:e=>n("notifications","jobCompletionNotifications",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"jobCompletionNotifications",className:"ml-2 block text-sm text-gray-900",children:"Job Completion Notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"paymentNotifications",checked:e.notifications.paymentNotifications,onChange:e=>n("notifications","paymentNotifications",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"paymentNotifications",className:"ml-2 block text-sm text-gray-900",children:"Payment Notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"systemAlerts",checked:e.notifications.systemAlerts,onChange:e=>n("notifications","systemAlerts",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"systemAlerts",className:"ml-2 block text-sm text-gray-900",children:"System Alerts"})]})]})]}),"security"===s&&(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Security Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Session Timeout (minutes)"}),a.jsx("input",{type:"number",min:"5",max:"120",value:e.security.sessionTimeout,onChange:e=>n("security","sessionTimeout",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password Minimum Length"}),a.jsx("input",{type:"number",min:"6",max:"20",value:e.security.passwordMinLength,onChange:e=>n("security","passwordMinLength",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Login Attempts"}),a.jsx("input",{type:"number",min:"3",max:"10",value:e.security.maxLoginAttempts,onChange:e=>n("security","maxLoginAttempts",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"requireTwoFactor",checked:e.security.requireTwoFactor,onChange:e=>n("security","requireTwoFactor",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"requireTwoFactor",className:"ml-2 block text-sm text-gray-900",children:"Require Two-Factor Authentication"})]})]}),a.jsx("div",{className:"mt-8 pt-6 border-t border-gray-200",children:a.jsx("button",{onClick:()=>{alert("Settings saved successfully!")},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-md text-sm font-medium",children:"Save Settings"})})]})]})}},2630:(e,t,s)=>{"use strict";var a,r,i,n,l;s.d(t,{O4:()=>a,WJ:()=>i,WZ:()=>r}),function(e){e.DataEntry="DataEntry",e.Accounting="Accounting",e.HR="HR",e.ITSupport="ITSupport",e.Marketing="Marketing",e.Legal="Legal",e.CustomerService="CustomerService",e.Other="Other"}(a||(a={})),function(e){e.DataProcessing="DataProcessing",e.DataCleaning="DataCleaning",e.DocumentationEntry="DocumentationEntry",e.Bookkeeping="Bookkeeping",e.FinancialReporting="FinancialReporting",e.Taxation="Taxation",e.Payroll="Payroll",e.Recruitment="Recruitment",e.EmployeeRelations="EmployeeRelations",e.Training="Training",e.CompensationBenefits="CompensationBenefits",e.TechnicalSupport="TechnicalSupport",e.NetworkSupport="NetworkSupport",e.SoftwareSupport="SoftwareSupport",e.HardwareSupport="HardwareSupport",e.DigitalMarketing="DigitalMarketing",e.ContentCreation="ContentCreation",e.SocialMedia="SocialMedia",e.MarketResearch="MarketResearch",e.ContractReview="ContractReview",e.Compliance="Compliance",e.LegalResearch="LegalResearch",e.Documentation="Documentation",e.CallCenter="CallCenter",e.EmailSupport="EmailSupport",e.ChatSupport="ChatSupport",e.CustomerFeedback="CustomerFeedback",e.Other="Other"}(r||(r={})),function(e){e.PDF="PDF",e.Word="Word",e.Excel="Excel",e.PlainText="PlainText",e.JSON="JSON",e.XML="XML",e.Database="Database",e.Other="Other"}(i||(i={})),function(e){e.Pending="Pending",e.UnderReview="UnderReview",e.Assigned="Assigned",e.InProgress="InProgress",e.Completed="Completed",e.Delivered="Delivered",e.Cancelled="Cancelled",e.OnHold="OnHold",e.Rejected="Rejected"}(n||(n={})),function(e){e.User="User",e.Admin="Admin",e.Supervisor="Supervisor",e.Staff="Staff"}(l||(l={}))},4753:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\admin\page.tsx#default`)},2029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,metadata:()=>n});var a=s(9510),r=s(5384),i=s.n(r);s(5023);let n={title:"Staff Hall",description:"A modern staffing solution"};function l({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:i().className,children:e})})}},3881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(6621);let r=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[948,349,621],()=>s(5408));module.exports=a})();