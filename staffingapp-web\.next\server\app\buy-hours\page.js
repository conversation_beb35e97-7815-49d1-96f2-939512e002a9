(()=>{var e={};e.id=340,e.ids=[340],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},9463:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(2354),r(2029),r(5866);var t=r(3191),a=r(8716),l=r(7922),o=r.n(l),i=r(5231),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);r.d(s,n);let d=["",{children:["buy-hours",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2354)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\buy-hours\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\buy-hours\\page.tsx"],m="/buy-hours/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/buy-hours/page",pathname:"/buy-hours",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6938:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},7286:()=>{},787:(e,s,r)=>{Promise.resolve().then(r.bind(r,867))},5047:(e,s,r)=>{"use strict";var t=r(7389);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}})},867:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(326),a=r(7577),l=r(5047);function o(){let e=(0,l.useRouter)(),[s,r]=(0,a.useState)(!1),[o,i]=(0,a.useState)(null),[n,d]=(0,a.useState)(null),[c,m]=(0,a.useState)(null),[u,x]=(0,a.useState)({hours:1,paymentReference:"",paymentProof:""}),[h,p]=(0,a.useState)({file:null,uploading:!1,uploadedUrl:null}),f=async()=>{if(!h.file)throw Error("No file selected");p(e=>({...e,uploading:!0}));try{let e=sessionStorage.getItem("token");if(!e)throw Error("Authentication required");let s=new FormData;s.append("file",h.file),s.append("type","payment-proof");let r=await fetch("http://localhost:5000/api/attachments/upload",{method:"POST",headers:{Authorization:`Bearer ${e}`},body:s});if(!r.ok)throw Error("Failed to upload file");let t=await r.json(),a=t.filePath||t.url||`uploads/${h.file.name}`;return p(e=>({...e,uploading:!1,uploadedUrl:a})),a}catch(e){throw p(e=>({...e,uploading:!1})),e}},g=async s=>{s.preventDefault(),r(!0),i(null),d(null);try{let s;let r=sessionStorage.getItem("token");if(!r)throw Error("Authentication required");if(!h.file&&!h.uploadedUrl)throw Error("Please select a payment proof file");let t=h.uploadedUrl;h.file&&!t&&(t=await f());let a={...u,paymentProof:t},l=await fetch("http://localhost:5000/api/HoursPurchase/purchase",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify(a)});if(!l.ok){console.error("Purchase API Error - Status:",l.status),console.error("Purchase API Error - Status Text:",l.statusText);let e="Failed to submit purchase request";try{e=(await l.json()).message||e}catch(r){console.error("Failed to parse error response as JSON:",r);let s=await l.text();console.error("Error response text:",s),e=`Server error (${l.status}): ${s||l.statusText}`}throw Error(e)}try{s=await l.json()}catch(s){console.error("Failed to parse success response as JSON:",s);let e=await l.text();throw console.error("Success response text:",e),Error("Server returned invalid response format")}d(`Purchase request submitted successfully! Reference ID: ${s.id}`),x({hours:1,paymentReference:"",paymentProof:""}),p({file:null,uploading:!1,uploadedUrl:null}),setTimeout(()=>{e.push("/dashboard")},2e3)}catch(e){console.error("Purchase error:",e),i(e instanceof Error?e.message:"Failed to submit purchase request")}finally{r(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[t.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between h-16",children:[(0,t.jsxs)("div",{className:"flex",children:[t.jsx("div",{className:"flex-shrink-0 flex items-center",children:t.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})}),(0,t.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[t.jsx("a",{href:"/dashboard",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),t.jsx("a",{href:"/submit-job",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Submit Job"}),t.jsx("a",{href:"/buy-hours",className:"border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Buy Hours"})]})]}),t.jsx("div",{className:"flex items-center",children:t.jsx("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:t.jsx("span",{children:"Sign Out"})})})]})})}),t.jsx("main",{className:"pt-24 py-10",children:t.jsx("div",{className:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8",children:t.jsx("div",{className:"bg-white shadow rounded-lg",children:(0,t.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Buy Hours"}),c&&t.jsx("div",{className:"bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("svg",{className:"h-5 w-5 text-emerald-400",viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[t.jsx("h3",{className:"text-sm font-medium text-emerald-800",children:"Current Rate"}),t.jsx("div",{className:"mt-2 text-sm text-emerald-700",children:(0,t.jsxs)("p",{children:["$",c.ratePerHour.toFixed(2)," ",c.currency," per hour"]})})]})]})}),o&&t.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[t.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),t.jsx("div",{className:"mt-2 text-sm text-red-700",children:t.jsx("p",{children:o})})]})]})}),n&&t.jsx("div",{className:"bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("svg",{className:"h-5 w-5 text-emerald-400",viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[t.jsx("h3",{className:"text-sm font-medium text-emerald-800",children:"Success"}),(0,t.jsxs)("div",{className:"mt-2 text-sm text-emerald-700",children:[t.jsx("p",{children:n}),t.jsx("p",{className:"mt-1 text-emerald-600",children:"Redirecting to dashboard in 2 seconds..."})]})]})]})}),(0,t.jsxs)("form",{onSubmit:g,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"hours",className:"block text-sm font-medium text-gray-700",children:"Number of Hours"}),t.jsx("input",{type:"number",id:"hours",min:"1",max:"1000",required:!0,value:u.hours,onChange:e=>x({...u,hours:parseInt(e.target.value)||1}),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm"})]}),c&&t.jsx("div",{className:"bg-gray-50 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Total Cost:"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["$",(c?u.hours*c.ratePerHour:0).toFixed(2)," ",c.currency]})]})}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"paymentReference",className:"block text-sm font-medium text-gray-700",children:"Payment Reference"}),t.jsx("input",{type:"text",id:"paymentReference",required:!0,placeholder:"e.g., Bank transfer reference, PayPal transaction ID",value:u.paymentReference,onChange:e=>x({...u,paymentReference:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm"})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"paymentProof",className:"block text-sm font-medium text-gray-700",children:"Payment Proof"}),t.jsx("div",{className:"mt-1",children:t.jsx("input",{type:"file",id:"paymentProof",accept:"image/*,.pdf",required:!0,onChange:e=>{let s=e.target.files?.[0];if(s){if(!["image/jpeg","image/jpg","image/png","image/gif","application/pdf"].includes(s.type)){i("Please upload an image (JPG, PNG, GIF) or PDF file");return}if(s.size>5242880){i("File size must be less than 5MB");return}p({file:s,uploading:!1,uploadedUrl:null}),i(null)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-emerald-50 file:text-emerald-700 hover:file:bg-emerald-100"})}),h.file&&t.jsx("div",{className:"mt-2 text-sm",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("svg",{className:"h-4 w-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,t.jsxs)("span",{className:"text-green-700",children:["Selected: ",h.file.name," (",(h.file.size/1024/1024).toFixed(2)," MB)"]})]})}),h.uploading&&t.jsx("div",{className:"mt-2 text-sm text-emerald-600",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("svg",{className:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[t.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),t.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),t.jsx("span",{children:"Uploading file..."})]})}),h.uploadedUrl&&t.jsx("div",{className:"mt-2 text-sm text-emerald-600",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})}),t.jsx("span",{children:"File uploaded successfully"})]})}),t.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Upload a screenshot or document proving your payment (JPG, PNG, GIF, or PDF, max 5MB)"})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[t.jsx("button",{type:"button",onClick:()=>e.push("/dashboard"),className:"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500",children:"Cancel"}),t.jsx("button",{type:"submit",disabled:s||h.uploading||!h.file&&!h.uploadedUrl,className:"bg-emerald-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50",children:h.uploading?"Uploading File...":s?"Submitting...":"Submit Purchase Request"})]})]}),t.jsx("div",{className:"mt-8 bg-yellow-50 border border-yellow-200 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[t.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"Payment Instructions"}),t.jsx("div",{className:"mt-2 text-sm text-yellow-700",children:(0,t.jsxs)("p",{children:["1. Calculate the total cost based on the current rate",t.jsx("br",{}),"2. Make the payment via your preferred method",t.jsx("br",{}),"3. Fill out this form with payment details",t.jsx("br",{}),"4. Your request will be reviewed and hours will be added to your account"]})})]})]})})]})})})})]})}},2354:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\buy-hours\page.tsx#default`)},2029:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i,metadata:()=>o});var t=r(9510),a=r(5384),l=r.n(a);r(5023);let o={title:"Staff Hall",description:"A modern staffing solution"};function i({children:e}){return t.jsx("html",{lang:"en",children:t.jsx("body",{className:l().className,children:e})})}},3881:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(6621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[948,349,621],()=>r(9463));module.exports=t})();