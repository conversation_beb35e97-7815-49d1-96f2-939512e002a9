(()=>{var e={};e.id=702,e.ids=[702],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},4168:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>d.a,__next_app__:()=>h,originalPathname:()=>c,pages:()=>x,routeModule:()=>p,tree:()=>o}),r(8256),r(2029),r(5866);var s=r(3191),i=r(8716),l=r(7922),d=r.n(l),n=r(5231),a={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>n[e]);r.d(t,a);let o=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8256)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],x=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\dashboard\\page.tsx"],c="/dashboard/page",h={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6938:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},7286:()=>{},7295:(e,t,r)=>{Promise.resolve().then(r.bind(r,4465))},5047:(e,t,r)=>{"use strict";var s=r(7389);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},4465:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(326),i=r(7577),l=r(5047);let d=e=>e.replace(/([A-Z])/g," $1").trim();function n({onError:e,hoursStatistics:t}){let r=(0,l.useRouter)(),[n,a]=(0,i.useState)([]),[o,x]=(0,i.useState)(!0),[c,h]=(0,i.useState)(null),[p,m]=(0,i.useState)(!1);if(!p)return(0,s.jsxs)("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"24px"},children:[s.jsx("h3",{style:{fontSize:"18px",fontWeight:"500",margin:"0 0 16px 0"},children:"Work History"}),s.jsx("p",{style:{color:"#6b7280",margin:"0"},children:"Loading..."})]});let u=e=>{let t={Pending:{bg:"#ecfdf5",text:"#065f46",border:"#d1fae5"},InProgress:{bg:"#f0fdf4",text:"#14532d",border:"#bbf7d0"},UnderReview:{bg:"#ecfdf5",text:"#047857",border:"#a7f3d0"},Completed:{bg:"#d1fae5",text:"#064e3b",border:"#6ee7b7"},Cancelled:{bg:"#f9fafb",text:"#6b7280",border:"#e5e7eb"},OnHold:{bg:"#f3f4f6",text:"#4b5563",border:"#d1d5db"}},r=t[e]||t.OnHold;return s.jsx("span",{style:{display:"inline-flex",alignItems:"center",padding:"4px 12px",borderRadius:"9999px",fontSize:"12px",fontWeight:"500",backgroundColor:r.bg,color:r.text,border:`1px solid ${r.border}`},children:d(e)})},g=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});if(o)return s.jsx("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"24px"},children:s.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"128px"},children:s.jsx("div",{style:{color:"#6b7280"},children:"Loading jobs..."})})});if(c)return s.jsx("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"24px"},children:s.jsx("div",{style:{borderRadius:"6px",backgroundColor:"#fef2f2",padding:"16px"},children:(0,s.jsxs)("div",{style:{display:"flex"},children:[s.jsx("div",{style:{flexShrink:0},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"#f87171"},viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{style:{marginLeft:"12px"},children:[s.jsx("h3",{style:{fontSize:"14px",fontWeight:"500",color:"#991b1b",margin:"0 0 8px 0"},children:"Error Loading Jobs"}),s.jsx("div",{style:{fontSize:"14px",color:"#7f1d1d",margin:"0"},children:c})]})]})})});if(0===n.length)return s.jsx("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px"},children:(0,s.jsxs)("div",{style:{padding:"24px"},children:[s.jsx("h3",{style:{fontSize:"18px",fontWeight:"500",color:"#111827",margin:"0 0 24px 0"},children:"Work History"}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"24px"},children:[s.jsx("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Total Jobs"}),s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:"0"})]})]})}),s.jsx("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Completed"}),s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:"0"})]})]})}),s.jsx("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"In Progress"}),s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:"0"})]})]})}),s.jsx("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Pending"}),s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:"0"})]})]})})]}),(0,s.jsxs)("div",{style:{textAlign:"center",padding:"32px 0"},children:[s.jsx("svg",{style:{margin:"0 auto",height:"48px",width:"48px",color:"#9ca3af"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),s.jsx("h3",{style:{marginTop:"8px",fontSize:"14px",fontWeight:"500",color:"#111827",margin:"8px 0"},children:"No jobs found"}),s.jsx("p",{style:{marginTop:"4px",fontSize:"14px",color:"#6b7280",margin:"4px 0 24px 0"},children:t&&t.hoursAvailable<=0?"Purchase hours to start submitting jobs.":"Get started by submitting your first job."}),t&&t.hoursAvailable>0&&s.jsx("div",{children:s.jsx("a",{href:"/submit-job",style:{display:"inline-flex",alignItems:"center",padding:"8px 16px",border:"none",boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",fontSize:"14px",fontWeight:"500",borderRadius:"6px",color:"white",backgroundColor:"#059669",textDecoration:"none"},children:"Submit Job"})})]})]})});let f=n.length,j=n.filter(e=>"Completed"===e.status).length,b=n.filter(e=>"Pending"===e.status).length,v=n.filter(e=>"InProgress"===e.status).length;return s.jsx("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px"},children:(0,s.jsxs)("div",{style:{padding:"24px"},children:[s.jsx("h3",{style:{fontSize:"18px",fontWeight:"500",color:"#111827",margin:"0 0 24px 0"},children:"Work History"}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"24px"},children:[s.jsx("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Total Jobs"}),s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:f})]})]})}),s.jsx("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Completed"}),s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:j})]})]})}),s.jsx("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"In Progress"}),s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:v})]})]})}),s.jsx("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[s.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:s.jsx("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Pending"}),s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:b})]})]})})]}),s.jsx("div",{style:{overflow:"hidden"},children:(0,s.jsxs)("table",{style:{minWidth:"100%",borderCollapse:"collapse"},children:[s.jsx("thead",{style:{backgroundColor:"#f9fafb"},children:(0,s.jsxs)("tr",{children:[s.jsx("th",{style:{padding:"12px 24px",textAlign:"left",fontSize:"12px",fontWeight:"500",color:"#6b7280",textTransform:"uppercase",letterSpacing:"0.05em"},children:"Job"}),s.jsx("th",{style:{padding:"12px 24px",textAlign:"left",fontSize:"12px",fontWeight:"500",color:"#6b7280",textTransform:"uppercase",letterSpacing:"0.05em"},children:"Type"}),s.jsx("th",{style:{padding:"12px 24px",textAlign:"left",fontSize:"12px",fontWeight:"500",color:"#6b7280",textTransform:"uppercase",letterSpacing:"0.05em"},children:"Status"}),s.jsx("th",{style:{padding:"12px 24px",textAlign:"left",fontSize:"12px",fontWeight:"500",color:"#6b7280",textTransform:"uppercase",letterSpacing:"0.05em"},children:"Created"})]})}),s.jsx("tbody",{style:{backgroundColor:"white"},children:n.map((e,t)=>(0,s.jsxs)("tr",{style:{borderTop:t>0?"1px solid #e5e7eb":"none"},children:[s.jsx("td",{style:{padding:"16px 24px",whiteSpace:"nowrap"},children:(0,s.jsxs)("div",{children:[s.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:"#059669",cursor:"pointer",textDecoration:"underline"},onClick:()=>r.push(`/jobs/${e.id}`),onMouseEnter:e=>{e.currentTarget.style.color="#047857"},onMouseLeave:e=>{e.currentTarget.style.color="#059669"},children:e.title}),s.jsx("div",{style:{fontSize:"14px",color:"#6b7280",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"300px"},children:e.description})]})}),(0,s.jsxs)("td",{style:{padding:"16px 24px",whiteSpace:"nowrap"},children:[s.jsx("div",{style:{fontSize:"14px",color:"#111827"},children:d(e.jobType)}),s.jsx("div",{style:{fontSize:"14px",color:"#6b7280"},children:d(e.category)})]}),s.jsx("td",{style:{padding:"16px 24px",whiteSpace:"nowrap"},children:u(e.status)}),s.jsx("td",{style:{padding:"16px 24px",whiteSpace:"nowrap",fontSize:"14px",color:"#6b7280"},children:g(e.createdAt)})]},e.id))})]})})]})})}function a(){let e=(0,l.useRouter)(),[t,r]=(0,i.useState)(!0),[d,a]=(0,i.useState)(null),[o,x]=(0,i.useState)(null),[c,h]=(0,i.useState)({hoursBought:0,hoursUtilized:0,hoursAvailable:0}),[p,m]=(0,i.useState)(null),[u,g]=(0,i.useState)(!0),f=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),j=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),b=e=>e.toFixed(2);return t?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[s.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex justify-between h-16",children:s.jsx("div",{className:"flex",children:s.jsx("div",{className:"flex-shrink-0 flex items-center",children:s.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),s.jsx("main",{className:"pt-24 py-10",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex justify-center items-center h-64",children:s.jsx("div",{className:"text-gray-500",children:"Loading statistics..."})})})})]}):d?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[s.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"flex justify-between h-16",children:s.jsx("div",{className:"flex",children:s.jsx("div",{className:"flex-shrink-0 flex items-center",children:s.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),s.jsx("main",{className:"pt-24 py-10",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"rounded-md bg-red-50 p-4 mb-4",children:(0,s.jsxs)("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[s.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error Loading Dashboard"}),s.jsx("div",{className:"mt-2 text-sm text-red-700",children:d}),s.jsx("div",{className:"mt-3",children:s.jsx("button",{onClick:()=>{},className:"bg-red-100 px-2 py-1 text-sm font-medium text-red-800 rounded-md hover:bg-red-200",children:"Retry"})})]})]})})})})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[s.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0 flex items-center",children:s.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})}),(0,s.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[s.jsx("a",{href:"/dashboard",className:"border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),s.jsx("a",{href:"/submit-job",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Submit Job"}),s.jsx("a",{href:"/buy-hours",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Buy Hours"})]})]}),s.jsx("div",{className:"flex items-center",children:s.jsx("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:s.jsx("span",{children:"Sign Out"})})})]})})}),s.jsx("main",{className:"pt-24 py-10",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[s.jsx("h1",{className:"text-2xl font-semibold text-gray-900 mb-8",children:"Account Summary"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3 mb-8",children:[s.jsx("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:s.jsx("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Hours Bought"}),s.jsx("div",{className:"text-3xl font-bold text-emerald-900",children:c.hoursBought})]})]})}),s.jsx("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:s.jsx("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Hours Used"}),s.jsx("div",{className:"text-3xl font-bold text-emerald-900",children:c.hoursUtilized})]})]})}),s.jsx("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:s.jsx("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Available"}),s.jsx("div",{className:"text-3xl font-bold text-emerald-900",children:c.hoursAvailable})]})]})})]}),c.hoursAvailable<=0&&s.jsx("div",{className:"mt-8 bg-emerald-50 border border-emerald-200 rounded-lg p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"h-8 w-8 text-emerald-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[s.jsx("h3",{className:"text-lg font-medium text-emerald-800",children:"No Hours Available"}),s.jsx("p",{className:"text-sm text-emerald-700",children:"You need to purchase hours to submit jobs and access our services."})]})]}),s.jsx("div",{className:"flex-shrink-0",children:(0,s.jsxs)("a",{href:"/buy-hours",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 shadow-sm",children:[s.jsx("svg",{className:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Buy Hours Now"]})})]})}),s.jsx("div",{style:{marginTop:"32px"},children:s.jsx(n,{hoursStatistics:c,onError:t=>{console.error("JobList error:",t),(t.includes("Authentication required")||t.includes("401"))&&e.push("/login")}})}),(0,s.jsxs)("div",{className:"mt-8 bg-white shadow rounded-lg p-6",children:[s.jsx("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Statement of Account"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[s.jsx("div",{className:"bg-emerald-50 p-4 rounded-xl border border-emerald-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:s.jsx("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Total Hours Bought"}),s.jsx("div",{className:"text-2xl font-bold text-emerald-900",children:p?b(p.totalHoursBought):b(c.hoursBought)})]})]})}),s.jsx("div",{className:"bg-emerald-50 p-4 rounded-xl border border-emerald-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:s.jsx("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Total Hours Spent"}),s.jsx("div",{className:"text-2xl font-bold text-emerald-900",children:p?b(p.totalHoursSpent):b(c.hoursUtilized)})]})]})}),s.jsx("div",{className:"bg-emerald-50 p-4 rounded-xl border border-emerald-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:s.jsx("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Hours Balance"}),s.jsx("div",{className:"text-2xl font-bold text-emerald-900",children:p?b(p.currentHoursBalance):b(c.hoursAvailable)})]})]})}),s.jsx("div",{className:"bg-emerald-50 p-4 rounded-xl border border-emerald-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:s.jsx("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"text-sm font-medium text-emerald-700",children:"Amount Balance"}),s.jsx("div",{className:"text-2xl font-bold text-emerald-900",children:p?f(p.currentAmountBalance):"$0.00"})]})]})})]}),s.jsx("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reference"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Transaction"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Hours Bought"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Hours Spent"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Adjustment"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Balance Hours"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),s.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Balance Amount"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:u?s.jsx("tr",{children:s.jsx("td",{colSpan:10,className:"px-6 py-8 text-center text-gray-500",children:"Loading transactions..."})}):p&&p.entries.length>0?p.entries.map((e,t)=>(0,s.jsxs)("tr",{className:t%2==0?"bg-white":"bg-gray-50",children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:j(e.date)}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"Purchase"===e.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.type})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.reference}),s.jsx("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.transaction}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:e.hoursBought>0?b(e.hoursBought):"-"}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:e.hoursSpent>0?b(e.hoursSpent):"-"}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:0!==e.adjustment?b(e.adjustment):"-"}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900",children:b(e.balanceHours)}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:0!==e.amount?f(e.amount):"-"}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900",children:f(e.balanceAmount)})]},t)):s.jsx("tr",{children:s.jsx("td",{colSpan:10,className:"px-6 py-8 text-center text-gray-500",children:"No transactions found. Purchase hours or submit jobs to see your statement."})})})]})})]})]})})]})}},8256:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\dashboard\page.tsx#default`)},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>d});var s=r(9510),i=r(5384),l=r.n(i);r(5023);let d={title:"Staff Hall",description:"A modern staffing solution"};function n({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:l().className,children:e})})}},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(6621);let i=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[948,349,621],()=>r(4168));module.exports=s})();