(()=>{var e={};e.id=670,e.ids=[670],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},177:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>p,routeModule:()=>x,tree:()=>l}),r(6891),r(2029),r(5866);var s=r(3191),i=r(8716),n=r(7922),o=r.n(n),a=r(5231),d={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l=["",{children:["debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6891)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\debug\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\debug\\page.tsx"],c="/debug/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/debug/page",pathname:"/debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6938:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},7286:()=>{},7638:(e,t,r)=>{Promise.resolve().then(r.bind(r,148))},148:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(326);function i(){return(0,s.jsxs)("div",{style:{margin:0,padding:"20px",fontFamily:"Arial, sans-serif",backgroundColor:"#f0f0f0",minHeight:"100vh"},children:[s.jsx("div",{style:{padding:"20px",margin:"10px 0",borderRadius:"8px",color:"white",fontSize:"18px",fontWeight:"bold",backgroundColor:"#ef4444"},children:"\uD83D\uDD34 RED BOX - If you can see this, basic HTML rendering works"}),s.jsx("div",{style:{padding:"20px",margin:"10px 0",borderRadius:"8px",color:"white",fontSize:"18px",fontWeight:"bold",backgroundColor:"#22c55e"},children:"\uD83D\uDFE2 GREEN BOX - This is a minimal Next.js page without any complex features"}),s.jsx("div",{style:{padding:"20px",margin:"10px 0",borderRadius:"8px",color:"white",fontSize:"18px",fontWeight:"bold",backgroundColor:"#3b82f6"},children:"\uD83D\uDD35 BLUE BOX - No authentication, no API calls, no complex components"}),(0,s.jsxs)("div",{style:{padding:"20px",backgroundColor:"white",borderRadius:"8px",marginTop:"20px"},children:[s.jsx("h2",{children:"Debug Information"}),s.jsx("p",{children:"If you can see this page, then:"}),(0,s.jsxs)("ul",{children:[s.jsx("li",{children:"✅ Next.js is running"}),s.jsx("li",{children:"✅ Basic routing works"}),s.jsx("li",{children:"✅ HTML rendering works"})]}),s.jsx("p",{children:"If you can see the colored boxes above, the hydration issue is fixed!"}),(0,s.jsxs)("div",{style:{marginTop:"20px"},children:[s.jsx("a",{href:"/test",style:{color:"#3b82f6",textDecoration:"underline"},children:"Go to Test Page"})," | ",s.jsx("a",{href:"/login",style:{color:"#3b82f6",textDecoration:"underline"},children:"Go to Login"})," | ",s.jsx("a",{href:"/dashboard",style:{color:"#3b82f6",textDecoration:"underline"},children:"Go to Dashboard"})]})]})]})}},6891:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\debug\page.tsx#default`)},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>o});var s=r(9510),i=r(5384),n=r.n(i);r(5023);let o={title:"Staff Hall",description:"A modern staffing solution"};function a({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:n().className,children:e})})}},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(6621);let i=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[948,349,621],()=>r(177));module.exports=s})();