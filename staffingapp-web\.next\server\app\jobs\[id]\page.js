(()=>{var e={};e.id=712,e.ids=[712],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},2896:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>o,originalPathname:()=>c,pages:()=>x,routeModule:()=>p,tree:()=>m}),t(2955),t(2029),t(5866);var a=t(3191),r=t(8716),d=t(7922),l=t.n(d),i=t(5231),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(s,n);let m=["",{children:["jobs",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2955)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\jobs\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],x=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\jobs\\[id]\\page.tsx"],c="/jobs/[id]/page",o={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/jobs/[id]/page",pathname:"/jobs/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},6938:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},7286:()=>{},5807:(e,s,t)=>{Promise.resolve().then(t.bind(t,5028))},5047:(e,s,t)=>{"use strict";var a=t(7389);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},5028:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(326),r=t(7577),d=t(5047);function l(){let e=(0,d.useRouter)(),s=(0,d.useParams)(),[t,l]=(0,r.useState)(null),[i,n]=(0,r.useState)(!0),[m,x]=(0,r.useState)(null);s.id&&parseInt(s.id);let c=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),o=e=>{let s={Pending:{bg:"#ecfdf5",text:"#065f46",border:"#d1fae5"},InProgress:{bg:"#f0fdf4",text:"#14532d",border:"#bbf7d0"},UnderReview:{bg:"#ecfdf5",text:"#047857",border:"#a7f3d0"},Completed:{bg:"#d1fae5",text:"#064e3b",border:"#6ee7b7"},Cancelled:{bg:"#f9fafb",text:"#6b7280",border:"#e5e7eb"},OnHold:{bg:"#f3f4f6",text:"#4b5563",border:"#d1d5db"}}[e]||{bg:"#f3f4f6",text:"#4b5563",border:"#d1d5db"};return a.jsx("span",{style:{backgroundColor:s.bg,color:s.text,border:`1px solid ${s.border}`,padding:"4px 12px",borderRadius:"9999px",fontSize:"12px",fontWeight:"500"},children:e})};return i?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[a.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex justify-between h-16",children:a.jsx("div",{className:"flex",children:a.jsx("div",{className:"flex-shrink-0 flex items-center",children:a.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),a.jsx("main",{className:"pt-24 py-10",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-600",children:"Loading job details..."})]})})})})]}):m?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[a.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex justify-between h-16",children:a.jsx("div",{className:"flex",children:a.jsx("div",{className:"flex-shrink-0 flex items-center",children:a.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),a.jsx("main",{className:"pt-24 py-10",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:a.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[a.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),a.jsx("div",{className:"mt-2 text-sm text-red-700",children:a.jsx("p",{children:m})}),a.jsx("div",{className:"mt-4",children:a.jsx("button",{onClick:()=>e.push("/dashboard"),className:"bg-emerald-100 hover:bg-emerald-200 text-emerald-800 px-4 py-2 rounded-md text-sm font-medium",children:"Back to Dashboard"})})]})]})})})})]}):t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[a.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex",children:[a.jsx("div",{className:"flex-shrink-0 flex items-center",children:a.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})}),(0,a.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[a.jsx("a",{href:"/dashboard",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),a.jsx("a",{href:"/submit-job",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Submit Job"}),a.jsx("a",{href:"/buy-hours",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Buy Hours"})]})]}),a.jsx("div",{className:"flex items-center",children:a.jsx("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:a.jsx("span",{children:"Sign Out"})})})]})})}),a.jsx("main",{className:"pt-24 py-10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("button",{onClick:()=>e.push("/dashboard"),className:"mb-4 text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center",children:[a.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Dashboard"]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:t.title}),o(t.status)]})]}),(0,a.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:[(0,a.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[a.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Job Information"}),a.jsx("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Complete details about this job."})]}),a.jsx("div",{className:"border-t border-gray-200",children:(0,a.jsxs)("dl",{children:[(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Job ID"}),(0,a.jsxs)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:["#",t.id]})]}),(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Description"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t.description})]}),(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Job Type"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t.jobType})]}),(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Category"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t.category})]}),(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Output Format"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t.outputFormat})]}),(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Status"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:o(t.status)})]}),(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Created"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:c(t.createdAt)})]}),t.updatedAt&&(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:c(t.updatedAt)})]}),t.attachmentUrl&&(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Attachment"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:a.jsx("a",{href:t.attachmentUrl,target:"_blank",rel:"noopener noreferrer",className:"text-emerald-600 hover:text-emerald-800 underline",children:"View Attachment"})})]}),t.referenceUrl&&(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"Reference URL"}),a.jsx("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:a.jsx("a",{href:t.referenceUrl,target:"_blank",rel:"noopener noreferrer",className:"text-emerald-600 hover:text-emerald-800 underline",children:"View Reference"})})]})]})})]})]})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[a.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsx("div",{className:"flex justify-between h-16",children:a.jsx("div",{className:"flex",children:a.jsx("div",{className:"flex-shrink-0 flex items-center",children:a.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),a.jsx("main",{className:"pt-24 py-10",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-gray-600",children:"Job not found"}),a.jsx("button",{onClick:()=>e.push("/dashboard"),className:"mt-4 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Back to Dashboard"})]})})})]})}},2955:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\jobs\[id]\page.tsx#default`)},2029:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,metadata:()=>l});var a=t(9510),r=t(5384),d=t.n(r);t(5023);let l={title:"Staff Hall",description:"A modern staffing solution"};function i({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:d().className,children:e})})}},3881:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(6621);let r=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[948,349,621],()=>t(2896));module.exports=a})();