(()=>{var e={};e.id=533,e.ids=[533],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},9888:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>o}),t(8452),t(2029),t(5866);var a=t(3191),r=t(8716),n=t(7922),i=t.n(n),l=t(5231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["staff",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8452)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\staff\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\staff\\page.tsx"],m="/staff/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/staff/page",pathname:"/staff",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6938:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},7286:()=>{},9794:(e,s,t)=>{Promise.resolve().then(t.bind(t,6512))},5047:(e,s,t)=>{"use strict";var a=t(7389);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6512:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(326),r=t(7577),n=t(5047);let i=e=>e.replace(/([A-Z])/g," $1").trim();function l(){let e=(0,n.useRouter)(),[s,t]=(0,r.useState)(!0),[l,d]=(0,r.useState)(null),[o,c]=(0,r.useState)([]),[m,x]=(0,r.useState)("assigned"),h=async()=>{try{let e=sessionStorage.getItem("token"),s=await fetch("http://localhost:5000/api/jobs/assigned",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(s.ok){let e=await s.json();c(e)}}catch(e){console.error("Error fetching assigned jobs:",e)}finally{t(!1)}},p=e=>o.filter(s=>s.status===e),g=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),u=async e=>{try{let s=sessionStorage.getItem("token");(await fetch(`http://localhost:5000/api/jobs/${e}/start`,{method:"PUT",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}})).ok&&h()}catch(e){console.error("Error starting job:",e)}},j=async e=>{try{let s=sessionStorage.getItem("token");(await fetch(`http://localhost:5000/api/jobs/${e}/complete`,{method:"PUT",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}})).ok&&h()}catch(e){console.error("Error completing job:",e)}};if(s)return a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-600",children:"Loading staff dashboard..."})]})});let f=p("Assigned"),b=p("InProgress"),N=p("Completed");return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[a.jsx("div",{className:"flex items-center",children:a.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"Staff Hall - Staff Dashboard"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",l?.firstName," ",l?.lastName]}),a.jsx("button",{onClick:()=>{sessionStorage.removeItem("token"),sessionStorage.removeItem("user"),e.push("/login")},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Logout"})]})]})})}),a.jsx("main",{className:"pt-24 py-10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[a.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsx("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white text-sm font-bold",children:f.length})})}),a.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"New Assignments"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[f.length," jobs"]})]})})]})})}),a.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsx("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white text-sm font-bold",children:b.length})})}),a.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"In Progress"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[b.length," jobs"]})]})})]})})}),a.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsx("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white text-sm font-bold",children:N.length})})}),a.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Completed"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[N.length," jobs"]})]})})]})})})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[a.jsx("div",{className:"border-b border-gray-200",children:a.jsx("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"assigned",name:"New Assignments",count:f.length},{id:"progress",name:"In Progress",count:b.length},{id:"completed",name:"Completed",count:N.length}].map(e=>(0,a.jsxs)("button",{onClick:()=>x(e.id),className:`${m===e.id?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`,children:[e.name,a.jsx("span",{className:"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs",children:e.count})]},e.id))})}),(0,a.jsxs)("div",{className:"p-6",children:["assigned"===m&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"New Job Assignments"}),0===f.length?a.jsx("p",{className:"text-gray-500 text-center py-8",children:"No new job assignments"}):a.jsx("div",{className:"space-y-4",children:f.map(s=>a.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),a.jsx("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",i(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",i(s.category)]}),(0,a.jsxs)("span",{children:["Assigned: ",s.assignedAt?g(s.assignedAt):"N/A"]})]}),s.supervisorNotes&&a.jsx("div",{className:"mt-2 p-2 bg-blue-50 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[a.jsx("strong",{children:"Supervisor Notes:"})," ",s.supervisorNotes]})})]}),(0,a.jsxs)("div",{className:"ml-4 flex space-x-2",children:[a.jsx("button",{onClick:()=>e.push(`/jobs/${s.id}`),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),a.jsx("button",{onClick:()=>u(s.id),className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded text-sm",children:"Start Working"})]})]})},s.id))})]}),"progress"===m&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs In Progress"}),0===b.length?a.jsx("p",{className:"text-gray-500 text-center py-8",children:"No jobs in progress"}):a.jsx("div",{className:"space-y-4",children:b.map(s=>a.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),a.jsx("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",i(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",i(s.category)]}),(0,a.jsxs)("span",{children:["Started: ",s.assignedAt?g(s.assignedAt):"N/A"]})]})]}),(0,a.jsxs)("div",{className:"ml-4 flex space-x-2",children:[a.jsx("button",{onClick:()=>e.push(`/jobs/${s.id}`),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),a.jsx("button",{onClick:()=>j(s.id),className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm",children:"Mark Complete"})]})]})},s.id))})]}),"completed"===m&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Completed Jobs"}),0===N.length?a.jsx("p",{className:"text-gray-500 text-center py-8",children:"No completed jobs"}):a.jsx("div",{className:"space-y-4",children:N.map(s=>a.jsx("div",{className:"border border-gray-200 rounded-lg p-4 bg-green-50",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),a.jsx("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",i(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",i(s.category)]}),(0,a.jsxs)("span",{children:["Completed: ",s.updatedAt?g(s.updatedAt):"N/A"]})]})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>e.push(`/jobs/${s.id}`),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),a.jsx("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800",children:"Completed"})]})]})},s.id))})]})]})]})]})})]})}},2029:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(9510),r=t(5384),n=t.n(r);t(5023);let i={title:"Staff Hall",description:"A modern staffing solution"};function l({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:n().className,children:e})})}},8452:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\staff\page.tsx#default`)},3881:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(6621);let r=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[948,349,621],()=>t(9888));module.exports=a})();