/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/submit-job/page";
exports.ids = ["app/submit-job/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?4c03":
/*!***********************!*\
  !*** debug (ignored) ***!
  \***********************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsubmit-job%2Fpage&page=%2Fsubmit-job%2Fpage&appPaths=%2Fsubmit-job%2Fpage&pagePath=private-next-app-dir%2Fsubmit-job%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsubmit-job%2Fpage&page=%2Fsubmit-job%2Fpage&appPaths=%2Fsubmit-job%2Fpage&pagePath=private-next-app-dir%2Fsubmit-job%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'submit-job',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/submit-job/page.tsx */ \"(rsc)/./src/app/submit-job/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/submit-job/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/submit-job/page\",\n        pathname: \"/submit-job\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsubmit-job%2Fpage&page=%2Fsubmit-job%2Fpage&appPaths=%2Fsubmit-job%2Fpage&pagePath=private-next-app-dir%2Fsubmit-job%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Csubmit-job%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Csubmit-job%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/submit-job/page.tsx */ \"(ssr)/./src/app/submit-job/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hvbWUlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q3N0YWZmaW5nYXBwJTVDJTVDc3RhZmZpbmdhcHAtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDc3VibWl0LWpvYiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBdUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvP2E4M2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIb21lXFxcXERvY3VtZW50c1xcXFxQcm9qZWN0c1xcXFxzdGFmZmluZ2FwcFxcXFxzdGFmZmluZ2FwcC13ZWJcXFxcc3JjXFxcXGFwcFxcXFxzdWJtaXQtam9iXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Csubmit-job%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/submit-job/page.tsx":
/*!*************************************!*\
  !*** ./src/app/submit-job/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SubmitJobPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _types_models__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/models */ \"(ssr)/./src/types/models.ts\");\n/* harmony import */ var _lib_api_statistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/statistics */ \"(ssr)/./src/lib/api/statistics.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// Utility function to format enum names with proper spacing\nconst formatEnumName = (enumValue)=>{\n    return enumValue.replace(/([A-Z])/g, \" $1\").trim();\n};\nfunction SubmitJobPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hoursLoading, setHoursLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [statistics, setStatistics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        hoursBought: 0,\n        hoursUtilized: 0,\n        hoursAvailable: 0\n    });\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        jobType: _types_models__WEBPACK_IMPORTED_MODULE_3__.JobType.DataEntry,\n        category: _types_models__WEBPACK_IMPORTED_MODULE_3__.JobCategory.DataProcessing,\n        outputFormat: _types_models__WEBPACK_IMPORTED_MODULE_3__.OutputFormat.PDF,\n        attachmentUrl: \"\",\n        referenceUrl: \"\",\n        clientId: 0\n    });\n    // Fetch hours statistics on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchHoursStatistics = async ()=>{\n            try {\n                setHoursLoading(true);\n                const data = await (0,_lib_api_statistics__WEBPACK_IMPORTED_MODULE_4__.getHoursStatistics)();\n                setStatistics(data);\n            } catch (err) {\n                console.error(\"Error fetching hours statistics:\", err);\n                // If authentication error, redirect to login\n                if (err instanceof Error && (err.message.includes(\"Authentication required\") || err.message.includes(\"401\"))) {\n                    router.push(\"/login\");\n                }\n            } finally{\n                setHoursLoading(false);\n            }\n        };\n        fetchHoursStatistics();\n    }, [\n        router\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsLoading(true);\n        setError(null);\n        // Check if user has available hours\n        if (statistics.hoursAvailable <= 0) {\n            setError(\"You need to purchase hours before submitting a job. Please buy hours first.\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const userStr = sessionStorage.getItem(\"user\");\n            const token = sessionStorage.getItem(\"token\");\n            // Debug: Log what we found in sessionStorage\n            console.log(\"Debug - sessionStorage contents:\", {\n                hasUser: !!userStr,\n                hasToken: !!token,\n                userStr: userStr,\n                tokenLength: token?.length || 0\n            });\n            if (!userStr || !token) {\n                throw new Error(\"Authentication required - Please login first\");\n            }\n            const user = JSON.parse(userStr);\n            const response = await fetch(\"http://localhost:5000/api/jobs\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    clientId: user.clientId\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.text();\n                throw new Error(`Failed to submit job: ${errorData}`);\n            }\n            // Show success message and redirect\n            setError(null);\n            // Create a temporary success notification\n            const successDiv = document.createElement(\"div\");\n            successDiv.className = \"fixed top-4 right-4 bg-emerald-50 border border-emerald-200 text-emerald-800 px-6 py-4 rounded-lg shadow-lg z-50 flex items-center\";\n            successDiv.innerHTML = `\n        <svg class=\"h-5 w-5 text-emerald-500 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"></path>\n        </svg>\n        <span class=\"font-medium\">Job submitted successfully! Redirecting to dashboard...</span>\n      `;\n            document.body.appendChild(successDiv);\n            // Remove the notification and redirect after 2 seconds\n            setTimeout(()=>{\n                document.body.removeChild(successDiv);\n                router.push(\"/dashboard\");\n            }, 2000);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to submit job\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed top-0 left-0 right-0 bg-white shadow-sm z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-emerald-600\",\n                                            children: \"Staff Hall\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:ml-6 sm:flex sm:space-x-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/dashboard\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/submit-job\",\n                                                className: \"border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Submit Job\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/buy-hours\",\n                                                className: \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\",\n                                                children: \"Buy Hours\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500\",\n                                    onClick: ()=>{\n                                        sessionStorage.clear();\n                                        document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n                                        window.location.href = \"/login\";\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-2xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold mb-8\",\n                                children: \"Submit New Job\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            !hoursLoading && statistics.hoursAvailable <= 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-emerald-500\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-emerald-800\",\n                                                    children: \"No Hours Available\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 text-sm text-emerald-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"You need to purchase hours before submitting a job. Please buy hours to continue.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/buy-hours\",\n                                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500\",\n                                                        children: \"Buy Hours Now\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, this),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-4 rounded-md mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"h-5 w-5 text-red-400\",\n                                                viewBox: \"0 0 20 20\",\n                                                fill: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-800\",\n                                                    children: \"Error Submitting Job\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 text-sm text-red-700\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6 bg-white shadow px-6 py-8 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"title\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Job Title *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                id: \"title\",\n                                                required: true,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                                value: formData.title,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        title: e.target.value\n                                                    }),\n                                                placeholder: \"Enter job title\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"description\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Description *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"description\",\n                                                required: true,\n                                                rows: 4,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                                value: formData.description,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        description: e.target.value\n                                                    }),\n                                                placeholder: \"Describe the job requirements and details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"jobType\",\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Job Type *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"jobType\",\n                                                        required: true,\n                                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                                        value: formData.jobType,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                jobType: e.target.value\n                                                            }),\n                                                        children: Object.values(_types_models__WEBPACK_IMPORTED_MODULE_3__.JobType).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: type,\n                                                                children: formatEnumName(type)\n                                                            }, type, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"category\",\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Category *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        id: \"category\",\n                                                        required: true,\n                                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                                        value: formData.category,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                category: e.target.value\n                                                            }),\n                                                        children: Object.values(_types_models__WEBPACK_IMPORTED_MODULE_3__.JobCategory).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: category,\n                                                                children: formatEnumName(category)\n                                                            }, category, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"outputFormat\",\n                                                className: \"block text-sm font-medium text-gray-700\",\n                                                children: \"Output Format *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"outputFormat\",\n                                                required: true,\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                                value: formData.outputFormat,\n                                                onChange: (e)=>setFormData({\n                                                        ...formData,\n                                                        outputFormat: e.target.value\n                                                    }),\n                                                children: Object.values(_types_models__WEBPACK_IMPORTED_MODULE_3__.OutputFormat).map((format)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: format,\n                                                        children: formatEnumName(format)\n                                                    }, format, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-6 sm:grid-cols-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"attachmentUrl\",\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Attachment URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"url\",\n                                                        id: \"attachmentUrl\",\n                                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                                        value: formData.attachmentUrl,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                attachmentUrl: e.target.value\n                                                            }),\n                                                        placeholder: \"https://example.com/attachment.pdf\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"referenceUrl\",\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Reference URL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"url\",\n                                                        id: \"referenceUrl\",\n                                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                                        value: formData.referenceUrl,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                referenceUrl: e.target.value\n                                                            }),\n                                                        placeholder: \"https://example.com/reference\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>router.push(\"/dashboard\"),\n                                                className: \"flex-1 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                disabled: isLoading || hoursLoading || statistics.hoursAvailable <= 0,\n                                                className: \"flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isLoading ? \"Submitting...\" : hoursLoading ? \"Loading...\" : statistics.hoursAvailable <= 0 ? \"Buy Hours to Submit Job\" : \"Submit Job\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\submit-job\\\\page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/submit-job/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/statistics.ts":
/*!***********************************!*\
  !*** ./src/lib/api/statistics.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAccountStatement: () => (/* binding */ getAccountStatement),\n/* harmony export */   getHoursStatistics: () => (/* binding */ getHoursStatistics)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_URL = \"http://localhost:5000\" || 0;\nconst getHoursStatistics = async ()=>{\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    const userStr = sessionStorage.getItem(\"user\");\n    if (!token || !userStr) {\n        throw new Error(\"Authentication required\");\n    }\n    const user = JSON.parse(userStr);\n    console.log(\"Fetching statistics for clientId:\", user.clientId);\n    try {\n        const headers = {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        };\n        console.log(\"Request headers:\", headers);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/statistics/hours/${user.clientId}`, {\n            headers\n        });\n        console.log(\"API Response:\", response.data);\n        return response.data;\n    } catch (error) {\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"API Error:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data,\n                clientId: user.clientId,\n                url: `${API_URL}/api/statistics/hours/${user.clientId}`\n            });\n            const status = error.response?.status;\n            const errorData = error.response?.data;\n            if (status === 401) {\n                throw new Error(\"Authentication required - please log in again\");\n            } else if (status === 404) {\n                throw new Error(\"Client data not found - please contact support\");\n            } else if (status === 500) {\n                throw new Error(\"Server error - please try again later\");\n            } else {\n                const message = typeof errorData === \"string\" ? errorData : errorData?.message || error.message || \"Unknown error occurred\";\n                throw new Error(`Failed to fetch statistics: ${message}`);\n            }\n        } else if (error instanceof Error) {\n            throw new Error(`Failed to fetch statistics: ${error.message}`);\n        } else {\n            throw new Error(\"Failed to fetch statistics: An unknown error occurred\");\n        }\n    }\n};\nconst getAccountStatement = async ()=>{\n    // Check if we're in the browser before accessing sessionStorage\n    if (true) {\n        throw new Error(\"Authentication required\");\n    }\n    const token = sessionStorage.getItem(\"token\");\n    const userStr = sessionStorage.getItem(\"user\");\n    if (!token || !userStr) {\n        throw new Error(\"Authentication required\");\n    }\n    const user = JSON.parse(userStr);\n    console.log(\"Fetching account statement for clientId:\", user.clientId);\n    try {\n        const headers = {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\"\n        };\n        console.log(\"Request headers:\", headers);\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${API_URL}/api/statistics/statement/${user.clientId}`, {\n            headers\n        });\n        console.log(\"Account Statement API Response:\", response.data);\n        return response.data;\n    } catch (error) {\n        if (axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isAxiosError(error)) {\n            console.error(\"Account Statement API Error:\", {\n                status: error.response?.status,\n                statusText: error.response?.statusText,\n                data: error.response?.data,\n                clientId: user.clientId,\n                url: `${API_URL}/api/statistics/statement/${user.clientId}`\n            });\n            const status = error.response?.status;\n            const errorData = error.response?.data;\n            if (status === 401) {\n                throw new Error(\"Authentication required - please log in again\");\n            } else if (status === 404) {\n                throw new Error(\"Account statement not found - please contact support\");\n            } else if (status === 500) {\n                throw new Error(\"Server error - please try again later\");\n            } else {\n                const message = typeof errorData === \"string\" ? errorData : errorData?.message || error.message || \"Unknown error occurred\";\n                throw new Error(`Failed to fetch account statement: ${message}`);\n            }\n        } else if (error instanceof Error) {\n            throw new Error(`Failed to fetch account statement: ${error.message}`);\n        } else {\n            throw new Error(\"Failed to fetch account statement: An unknown error occurred\");\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS9zdGF0aXN0aWNzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEwQjtBQUcxQixNQUFNQyxVQUFVQyx1QkFBK0IsSUFBSTtBQUU1QyxNQUFNRyxxQkFBcUI7SUFDOUIsZ0VBQWdFO0lBQ2hFLElBQUksSUFBa0IsRUFBYTtRQUMvQixNQUFNLElBQUlDLE1BQU07SUFDcEI7SUFFQSxNQUFNQyxRQUFRQyxlQUFlQyxPQUFPLENBQUM7SUFDckMsTUFBTUMsVUFBVUYsZUFBZUMsT0FBTyxDQUFDO0lBRXZDLElBQUksQ0FBQ0YsU0FBUyxDQUFDRyxTQUFTO1FBQ3BCLE1BQU0sSUFBSUosTUFBTTtJQUNwQjtJQUVBLE1BQU1LLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0g7SUFDeEJJLFFBQVFDLEdBQUcsQ0FBQyxxQ0FBcUNKLEtBQUtLLFFBQVE7SUFFOUQsSUFBSTtRQUNBLE1BQU1DLFVBQVU7WUFDWixpQkFBaUIsQ0FBQyxPQUFPLEVBQUVWLE1BQU0sQ0FBQztZQUNsQyxnQkFBZ0I7UUFDcEI7UUFDQU8sUUFBUUMsR0FBRyxDQUFDLG9CQUFvQkU7UUFFaEMsTUFBTUMsV0FBVyxNQUFNbEIsNkNBQUtBLENBQUNtQixHQUFHLENBQzVCLENBQUMsRUFBRWxCLFFBQVEsc0JBQXNCLEVBQUVVLEtBQUtLLFFBQVEsQ0FBQyxDQUFDLEVBQ2xEO1lBQUVDO1FBQVE7UUFHZEgsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkcsU0FBU0UsSUFBSTtRQUMxQyxPQUFPRixTQUFTRSxJQUFJO0lBQ3hCLEVBQUUsT0FBT0MsT0FBZ0I7UUFDckIsSUFBSXJCLDZDQUFLQSxDQUFDc0IsWUFBWSxDQUFDRCxRQUFRO1lBQzNCUCxRQUFRTyxLQUFLLENBQUMsY0FBYztnQkFDeEJFLFFBQVFGLE1BQU1ILFFBQVEsRUFBRUs7Z0JBQ3hCQyxZQUFZSCxNQUFNSCxRQUFRLEVBQUVNO2dCQUM1QkosTUFBTUMsTUFBTUgsUUFBUSxFQUFFRTtnQkFDdEJKLFVBQVVMLEtBQUtLLFFBQVE7Z0JBQ3ZCUyxLQUFLLENBQUMsRUFBRXhCLFFBQVEsc0JBQXNCLEVBQUVVLEtBQUtLLFFBQVEsQ0FBQyxDQUFDO1lBQzNEO1lBRUEsTUFBTU8sU0FBU0YsTUFBTUgsUUFBUSxFQUFFSztZQUMvQixNQUFNRyxZQUFZTCxNQUFNSCxRQUFRLEVBQUVFO1lBRWxDLElBQUlHLFdBQVcsS0FBSztnQkFDaEIsTUFBTSxJQUFJakIsTUFBTTtZQUNwQixPQUFPLElBQUlpQixXQUFXLEtBQUs7Z0JBQ3ZCLE1BQU0sSUFBSWpCLE1BQU07WUFDcEIsT0FBTyxJQUFJaUIsV0FBVyxLQUFLO2dCQUN2QixNQUFNLElBQUlqQixNQUFNO1lBQ3BCLE9BQU87Z0JBQ0gsTUFBTXFCLFVBQVUsT0FBT0QsY0FBYyxXQUFXQSxZQUNqQ0EsV0FBV0MsV0FBV04sTUFBTU0sT0FBTyxJQUFJO2dCQUN0RCxNQUFNLElBQUlyQixNQUFNLENBQUMsNEJBQTRCLEVBQUVxQixRQUFRLENBQUM7WUFDNUQ7UUFDSixPQUFPLElBQUlOLGlCQUFpQmYsT0FBTztZQUMvQixNQUFNLElBQUlBLE1BQU0sQ0FBQyw0QkFBNEIsRUFBRWUsTUFBTU0sT0FBTyxDQUFDLENBQUM7UUFDbEUsT0FBTztZQUNILE1BQU0sSUFBSXJCLE1BQU07UUFDcEI7SUFDSjtBQUNKLEVBQUU7QUFFSyxNQUFNc0Isc0JBQXNCO0lBQy9CLGdFQUFnRTtJQUNoRSxJQUFJLElBQWtCLEVBQWE7UUFDL0IsTUFBTSxJQUFJdEIsTUFBTTtJQUNwQjtJQUVBLE1BQU1DLFFBQVFDLGVBQWVDLE9BQU8sQ0FBQztJQUNyQyxNQUFNQyxVQUFVRixlQUFlQyxPQUFPLENBQUM7SUFFdkMsSUFBSSxDQUFDRixTQUFTLENBQUNHLFNBQVM7UUFDcEIsTUFBTSxJQUFJSixNQUFNO0lBQ3BCO0lBRUEsTUFBTUssT0FBT0MsS0FBS0MsS0FBSyxDQUFDSDtJQUN4QkksUUFBUUMsR0FBRyxDQUFDLDRDQUE0Q0osS0FBS0ssUUFBUTtJQUVyRSxJQUFJO1FBQ0EsTUFBTUMsVUFBVTtZQUNaLGlCQUFpQixDQUFDLE9BQU8sRUFBRVYsTUFBTSxDQUFDO1lBQ2xDLGdCQUFnQjtRQUNwQjtRQUNBTyxRQUFRQyxHQUFHLENBQUMsb0JBQW9CRTtRQUVoQyxNQUFNQyxXQUFXLE1BQU1sQiw2Q0FBS0EsQ0FBQ21CLEdBQUcsQ0FDNUIsQ0FBQyxFQUFFbEIsUUFBUSwwQkFBMEIsRUFBRVUsS0FBS0ssUUFBUSxDQUFDLENBQUMsRUFDdEQ7WUFBRUM7UUFBUTtRQUdkSCxRQUFRQyxHQUFHLENBQUMsbUNBQW1DRyxTQUFTRSxJQUFJO1FBQzVELE9BQU9GLFNBQVNFLElBQUk7SUFDeEIsRUFBRSxPQUFPQyxPQUFnQjtRQUNyQixJQUFJckIsNkNBQUtBLENBQUNzQixZQUFZLENBQUNELFFBQVE7WUFDM0JQLFFBQVFPLEtBQUssQ0FBQyxnQ0FBZ0M7Z0JBQzFDRSxRQUFRRixNQUFNSCxRQUFRLEVBQUVLO2dCQUN4QkMsWUFBWUgsTUFBTUgsUUFBUSxFQUFFTTtnQkFDNUJKLE1BQU1DLE1BQU1ILFFBQVEsRUFBRUU7Z0JBQ3RCSixVQUFVTCxLQUFLSyxRQUFRO2dCQUN2QlMsS0FBSyxDQUFDLEVBQUV4QixRQUFRLDBCQUEwQixFQUFFVSxLQUFLSyxRQUFRLENBQUMsQ0FBQztZQUMvRDtZQUVBLE1BQU1PLFNBQVNGLE1BQU1ILFFBQVEsRUFBRUs7WUFDL0IsTUFBTUcsWUFBWUwsTUFBTUgsUUFBUSxFQUFFRTtZQUVsQyxJQUFJRyxXQUFXLEtBQUs7Z0JBQ2hCLE1BQU0sSUFBSWpCLE1BQU07WUFDcEIsT0FBTyxJQUFJaUIsV0FBVyxLQUFLO2dCQUN2QixNQUFNLElBQUlqQixNQUFNO1lBQ3BCLE9BQU8sSUFBSWlCLFdBQVcsS0FBSztnQkFDdkIsTUFBTSxJQUFJakIsTUFBTTtZQUNwQixPQUFPO2dCQUNILE1BQU1xQixVQUFVLE9BQU9ELGNBQWMsV0FBV0EsWUFDakNBLFdBQVdDLFdBQVdOLE1BQU1NLE9BQU8sSUFBSTtnQkFDdEQsTUFBTSxJQUFJckIsTUFBTSxDQUFDLG1DQUFtQyxFQUFFcUIsUUFBUSxDQUFDO1lBQ25FO1FBQ0osT0FBTyxJQUFJTixpQkFBaUJmLE9BQU87WUFDL0IsTUFBTSxJQUFJQSxNQUFNLENBQUMsbUNBQW1DLEVBQUVlLE1BQU1NLE9BQU8sQ0FBQyxDQUFDO1FBQ3pFLE9BQU87WUFDSCxNQUFNLElBQUlyQixNQUFNO1FBQ3BCO0lBQ0o7QUFDSixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhZmZpbmdhcHAtd2ViLy4vc3JjL2xpYi9hcGkvc3RhdGlzdGljcy50cz9jMGE2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBheGlvcyBmcm9tICdheGlvcyc7XHJcbmltcG9ydCB7IEhvdXJzU3RhdGlzdGljcywgQWNjb3VudFN0YXRlbWVudCB9IGZyb20gJ0AvdHlwZXMvbW9kZWxzJztcclxuXHJcbmNvbnN0IEFQSV9VUkwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjUwMDAnO1xyXG5cclxuZXhwb3J0IGNvbnN0IGdldEhvdXJzU3RhdGlzdGljcyA9IGFzeW5jICgpOiBQcm9taXNlPEhvdXJzU3RhdGlzdGljcz4gPT4ge1xyXG4gICAgLy8gQ2hlY2sgaWYgd2UncmUgaW4gdGhlIGJyb3dzZXIgYmVmb3JlIGFjY2Vzc2luZyBzZXNzaW9uU3RvcmFnZVxyXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHRva2VuID0gc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKTtcclxuICAgIGNvbnN0IHVzZXJTdHIgPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCd1c2VyJyk7XHJcblxyXG4gICAgaWYgKCF0b2tlbiB8fCAhdXNlclN0cikge1xyXG4gICAgICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQnKTtcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCB1c2VyID0gSlNPTi5wYXJzZSh1c2VyU3RyKTtcclxuICAgIGNvbnNvbGUubG9nKCdGZXRjaGluZyBzdGF0aXN0aWNzIGZvciBjbGllbnRJZDonLCB1c2VyLmNsaWVudElkKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGhlYWRlcnMgPSB7XHJcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICB9O1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdSZXF1ZXN0IGhlYWRlcnM6JywgaGVhZGVycyk7XHJcblxyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KFxyXG4gICAgICAgICAgICBgJHtBUElfVVJMfS9hcGkvc3RhdGlzdGljcy9ob3Vycy8ke3VzZXIuY2xpZW50SWR9YCxcclxuICAgICAgICAgICAgeyBoZWFkZXJzIH1cclxuICAgICAgICApO1xyXG5cclxuICAgICAgICBjb25zb2xlLmxvZygnQVBJIFJlc3BvbnNlOicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3I6IHVua25vd24pIHtcclxuICAgICAgICBpZiAoYXhpb3MuaXNBeGlvc0Vycm9yKGVycm9yKSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdBUEkgRXJyb3I6Jywge1xyXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgc3RhdHVzVGV4dDogZXJyb3IucmVzcG9uc2U/LnN0YXR1c1RleHQsXHJcbiAgICAgICAgICAgICAgICBkYXRhOiBlcnJvci5yZXNwb25zZT8uZGF0YSxcclxuICAgICAgICAgICAgICAgIGNsaWVudElkOiB1c2VyLmNsaWVudElkLFxyXG4gICAgICAgICAgICAgICAgdXJsOiBgJHtBUElfVVJMfS9hcGkvc3RhdGlzdGljcy9ob3Vycy8ke3VzZXIuY2xpZW50SWR9YFxyXG4gICAgICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IHN0YXR1cyA9IGVycm9yLnJlc3BvbnNlPy5zdGF0dXM7XHJcbiAgICAgICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGVycm9yLnJlc3BvbnNlPy5kYXRhO1xyXG5cclxuICAgICAgICAgICAgaWYgKHN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkIC0gcGxlYXNlIGxvZyBpbiBhZ2FpbicpO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHN0YXR1cyA9PT0gNDA0KSB7XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NsaWVudCBkYXRhIG5vdCBmb3VuZCAtIHBsZWFzZSBjb250YWN0IHN1cHBvcnQnKTtcclxuICAgICAgICAgICAgfSBlbHNlIGlmIChzdGF0dXMgPT09IDUwMCkge1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdTZXJ2ZXIgZXJyb3IgLSBwbGVhc2UgdHJ5IGFnYWluIGxhdGVyJyk7XHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gdHlwZW9mIGVycm9yRGF0YSA9PT0gJ3N0cmluZycgPyBlcnJvckRhdGEgOlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JEYXRhPy5tZXNzYWdlIHx8IGVycm9yLm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3Igb2NjdXJyZWQnO1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggc3RhdGlzdGljczogJHttZXNzYWdlfWApO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHN0YXRpc3RpY3M6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBzdGF0aXN0aWNzOiBBbiB1bmtub3duIGVycm9yIG9jY3VycmVkJyk7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59O1xyXG5cclxuZXhwb3J0IGNvbnN0IGdldEFjY291bnRTdGF0ZW1lbnQgPSBhc3luYyAoKTogUHJvbWlzZTxBY2NvdW50U3RhdGVtZW50PiA9PiB7XHJcbiAgICAvLyBDaGVjayBpZiB3ZSdyZSBpbiB0aGUgYnJvd3NlciBiZWZvcmUgYWNjZXNzaW5nIHNlc3Npb25TdG9yYWdlXHJcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJyk7XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgdG9rZW4gPSBzZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpO1xyXG4gICAgY29uc3QgdXNlclN0ciA9IHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3VzZXInKTtcclxuXHJcbiAgICBpZiAoIXRva2VuIHx8ICF1c2VyU3RyKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHVzZXIgPSBKU09OLnBhcnNlKHVzZXJTdHIpO1xyXG4gICAgY29uc29sZS5sb2coJ0ZldGNoaW5nIGFjY291bnQgc3RhdGVtZW50IGZvciBjbGllbnRJZDonLCB1c2VyLmNsaWVudElkKTtcclxuXHJcbiAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGhlYWRlcnMgPSB7XHJcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICB9O1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdSZXF1ZXN0IGhlYWRlcnM6JywgaGVhZGVycyk7XHJcblxyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3MuZ2V0KFxyXG4gICAgICAgICAgICBgJHtBUElfVVJMfS9hcGkvc3RhdGlzdGljcy9zdGF0ZW1lbnQvJHt1c2VyLmNsaWVudElkfWAsXHJcbiAgICAgICAgICAgIHsgaGVhZGVycyB9XHJcbiAgICAgICAgKTtcclxuXHJcbiAgICAgICAgY29uc29sZS5sb2coJ0FjY291bnQgU3RhdGVtZW50IEFQSSBSZXNwb25zZTonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgIH0gY2F0Y2ggKGVycm9yOiB1bmtub3duKSB7XHJcbiAgICAgICAgaWYgKGF4aW9zLmlzQXhpb3NFcnJvcihlcnJvcikpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcignQWNjb3VudCBTdGF0ZW1lbnQgQVBJIEVycm9yOicsIHtcclxuICAgICAgICAgICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcclxuICAgICAgICAgICAgICAgIHN0YXR1c1RleHQ6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXNUZXh0LFxyXG4gICAgICAgICAgICAgICAgZGF0YTogZXJyb3IucmVzcG9uc2U/LmRhdGEsXHJcbiAgICAgICAgICAgICAgICBjbGllbnRJZDogdXNlci5jbGllbnRJZCxcclxuICAgICAgICAgICAgICAgIHVybDogYCR7QVBJX1VSTH0vYXBpL3N0YXRpc3RpY3Mvc3RhdGVtZW50LyR7dXNlci5jbGllbnRJZH1gXHJcbiAgICAgICAgICAgIH0pO1xyXG5cclxuICAgICAgICAgICAgY29uc3Qgc3RhdHVzID0gZXJyb3IucmVzcG9uc2U/LnN0YXR1cztcclxuICAgICAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gZXJyb3IucmVzcG9uc2U/LmRhdGE7XHJcblxyXG4gICAgICAgICAgICBpZiAoc3RhdHVzID09PSA0MDEpIHtcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQgLSBwbGVhc2UgbG9nIGluIGFnYWluJyk7XHJcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoc3RhdHVzID09PSA0MDQpIHtcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignQWNjb3VudCBzdGF0ZW1lbnQgbm90IGZvdW5kIC0gcGxlYXNlIGNvbnRhY3Qgc3VwcG9ydCcpO1xyXG4gICAgICAgICAgICB9IGVsc2UgaWYgKHN0YXR1cyA9PT0gNTAwKSB7XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NlcnZlciBlcnJvciAtIHBsZWFzZSB0cnkgYWdhaW4gbGF0ZXInKTtcclxuICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2UgPSB0eXBlb2YgZXJyb3JEYXRhID09PSAnc3RyaW5nJyA/IGVycm9yRGF0YSA6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvckRhdGE/Lm1lc3NhZ2UgfHwgZXJyb3IubWVzc2FnZSB8fCAnVW5rbm93biBlcnJvciBvY2N1cnJlZCc7XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBhY2NvdW50IHN0YXRlbWVudDogJHttZXNzYWdlfWApO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSBlbHNlIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGFjY291bnQgc3RhdGVtZW50OiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggYWNjb3VudCBzdGF0ZW1lbnQ6IEFuIHVua25vd24gZXJyb3Igb2NjdXJyZWQnKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn07Il0sIm5hbWVzIjpbImF4aW9zIiwiQVBJX1VSTCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19BUElfVVJMIiwiZ2V0SG91cnNTdGF0aXN0aWNzIiwiRXJyb3IiLCJ0b2tlbiIsInNlc3Npb25TdG9yYWdlIiwiZ2V0SXRlbSIsInVzZXJTdHIiLCJ1c2VyIiwiSlNPTiIsInBhcnNlIiwiY29uc29sZSIsImxvZyIsImNsaWVudElkIiwiaGVhZGVycyIsInJlc3BvbnNlIiwiZ2V0IiwiZGF0YSIsImVycm9yIiwiaXNBeGlvc0Vycm9yIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsInVybCIsImVycm9yRGF0YSIsIm1lc3NhZ2UiLCJnZXRBY2NvdW50U3RhdGVtZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/statistics.ts\n");

/***/ }),

/***/ "(ssr)/./src/types/models.ts":
/*!*****************************!*\
  !*** ./src/types/models.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JobCategory: () => (/* binding */ JobCategory),\n/* harmony export */   JobStatus: () => (/* binding */ JobStatus),\n/* harmony export */   JobType: () => (/* binding */ JobType),\n/* harmony export */   OutputFormat: () => (/* binding */ OutputFormat),\n/* harmony export */   UserRole: () => (/* binding */ UserRole)\n/* harmony export */ });\nvar JobType;\n(function(JobType) {\n    JobType[\"DataEntry\"] = \"DataEntry\";\n    JobType[\"Accounting\"] = \"Accounting\";\n    JobType[\"HR\"] = \"HR\";\n    JobType[\"ITSupport\"] = \"ITSupport\";\n    JobType[\"Marketing\"] = \"Marketing\";\n    JobType[\"Legal\"] = \"Legal\";\n    JobType[\"CustomerService\"] = \"CustomerService\";\n    JobType[\"Other\"] = \"Other\";\n})(JobType || (JobType = {}));\nvar JobCategory;\n(function(JobCategory) {\n    // Data Entry Categories\n    JobCategory[\"DataProcessing\"] = \"DataProcessing\";\n    JobCategory[\"DataCleaning\"] = \"DataCleaning\";\n    JobCategory[\"DocumentationEntry\"] = \"DocumentationEntry\";\n    // Accounting Categories\n    JobCategory[\"Bookkeeping\"] = \"Bookkeeping\";\n    JobCategory[\"FinancialReporting\"] = \"FinancialReporting\";\n    JobCategory[\"Taxation\"] = \"Taxation\";\n    JobCategory[\"Payroll\"] = \"Payroll\";\n    // HR Categories\n    JobCategory[\"Recruitment\"] = \"Recruitment\";\n    JobCategory[\"EmployeeRelations\"] = \"EmployeeRelations\";\n    JobCategory[\"Training\"] = \"Training\";\n    JobCategory[\"CompensationBenefits\"] = \"CompensationBenefits\";\n    // IT Support Categories\n    JobCategory[\"TechnicalSupport\"] = \"TechnicalSupport\";\n    JobCategory[\"NetworkSupport\"] = \"NetworkSupport\";\n    JobCategory[\"SoftwareSupport\"] = \"SoftwareSupport\";\n    JobCategory[\"HardwareSupport\"] = \"HardwareSupport\";\n    // Marketing Categories\n    JobCategory[\"DigitalMarketing\"] = \"DigitalMarketing\";\n    JobCategory[\"ContentCreation\"] = \"ContentCreation\";\n    JobCategory[\"SocialMedia\"] = \"SocialMedia\";\n    JobCategory[\"MarketResearch\"] = \"MarketResearch\";\n    // Legal Categories\n    JobCategory[\"ContractReview\"] = \"ContractReview\";\n    JobCategory[\"Compliance\"] = \"Compliance\";\n    JobCategory[\"LegalResearch\"] = \"LegalResearch\";\n    JobCategory[\"Documentation\"] = \"Documentation\";\n    // Customer Service Categories\n    JobCategory[\"CallCenter\"] = \"CallCenter\";\n    JobCategory[\"EmailSupport\"] = \"EmailSupport\";\n    JobCategory[\"ChatSupport\"] = \"ChatSupport\";\n    JobCategory[\"CustomerFeedback\"] = \"CustomerFeedback\";\n    // Other\n    JobCategory[\"Other\"] = \"Other\";\n})(JobCategory || (JobCategory = {}));\nvar OutputFormat;\n(function(OutputFormat) {\n    OutputFormat[\"PDF\"] = \"PDF\";\n    OutputFormat[\"Word\"] = \"Word\";\n    OutputFormat[\"Excel\"] = \"Excel\";\n    OutputFormat[\"PlainText\"] = \"PlainText\";\n    OutputFormat[\"JSON\"] = \"JSON\";\n    OutputFormat[\"XML\"] = \"XML\";\n    OutputFormat[\"Database\"] = \"Database\";\n    OutputFormat[\"Other\"] = \"Other\";\n})(OutputFormat || (OutputFormat = {}));\nvar JobStatus;\n(function(JobStatus) {\n    JobStatus[\"Pending\"] = \"Pending\";\n    JobStatus[\"UnderReview\"] = \"UnderReview\";\n    JobStatus[\"Assigned\"] = \"Assigned\";\n    JobStatus[\"InProgress\"] = \"InProgress\";\n    JobStatus[\"Completed\"] = \"Completed\";\n    JobStatus[\"UnderSupervisorReview\"] = \"UnderSupervisorReview\";\n    JobStatus[\"Delivered\"] = \"Delivered\";\n    JobStatus[\"Satisfied\"] = \"Satisfied\";\n    JobStatus[\"Cancelled\"] = \"Cancelled\";\n    JobStatus[\"OnHold\"] = \"OnHold\";\n    JobStatus[\"Rejected\"] = \"Rejected\";\n})(JobStatus || (JobStatus = {}));\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"User\"] = \"User\";\n    UserRole[\"Admin\"] = \"Admin\";\n    UserRole[\"Supervisor\"] = \"Supervisor\";\n    UserRole[\"Staff\"] = \"Staff\";\n})(UserRole || (UserRole = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/types/models.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"927152f9cf66\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhZmZpbmdhcHAtd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83NzkzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTI3MTUyZjljZjY2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Staff Hall\",\n    description: \"A modern staffing solution\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHTUE7QUFIZ0I7QUFLZixNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULCtKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1N0YWZmIEhhbGwnLFxuICBkZXNjcmlwdGlvbjogJ0EgbW9kZXJuIHN0YWZmaW5nIHNvbHV0aW9uJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/submit-job/page.tsx":
/*!*************************************!*\
  !*** ./src/app/submit-job/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\submit-job\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2Zhdmljb24uaWNvP2VhNzAiXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/get-intrinsic","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsubmit-job%2Fpage&page=%2Fsubmit-job%2Fpage&appPaths=%2Fsubmit-job%2Fpage&pagePath=private-next-app-dir%2Fsubmit-job%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();