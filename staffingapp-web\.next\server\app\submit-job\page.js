(()=>{var e={};e.id=845,e.ids=[845],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},7656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(7725),r(2029),r(5866);var s=r(3191),a=r(8716),o=r(7922),n=r.n(o),l=r(5231),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d=["",{children:["submit-job",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7725)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\submit-job\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\submit-job\\page.tsx"],m="/submit-job/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/submit-job/page",pathname:"/submit-job",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6938:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2994,23)),Promise.resolve().then(r.t.bind(r,6114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,9671,23)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,4759,23))},7286:()=>{},3975:(e,t,r)=>{Promise.resolve().then(r.bind(r,7663))},5047:(e,t,r)=>{"use strict";var s=r(7389);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},7663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(326),a=r(7577),o=r(5047),n=r(2630);let l=e=>e.replace(/([A-Z])/g," $1").trim();function i(){let e=(0,o.useRouter)(),[t,r]=(0,a.useState)(!1),[i,d]=(0,a.useState)(null),[c,m]=(0,a.useState)(!0),[u,p]=(0,a.useState)({hoursBought:0,hoursUtilized:0,hoursAvailable:0}),[h,x]=(0,a.useState)({title:"",description:"",jobType:n.O4.DataEntry,category:n.WZ.DataProcessing,outputFormat:n.WJ.PDF,attachmentUrl:"",referenceUrl:"",clientId:0}),b=async t=>{if(t.preventDefault(),r(!0),d(null),u.hoursAvailable<=0){d("You need to purchase hours before submitting a job. Please buy hours first."),r(!1);return}try{let t=sessionStorage.getItem("user"),r=sessionStorage.getItem("token");if(console.log("Debug - sessionStorage contents:",{hasUser:!!t,hasToken:!!r,userStr:t,tokenLength:r?.length||0}),!t||!r)throw Error("Authentication required - Please login first");let s=JSON.parse(t),a=await fetch("http://localhost:5000/api/jobs",{method:"POST",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify({...h,clientId:s.clientId})});if(!a.ok){let e=await a.text();throw Error(`Failed to submit job: ${e}`)}d(null);let o=document.createElement("div");o.className="fixed top-4 right-4 bg-emerald-50 border border-emerald-200 text-emerald-800 px-6 py-4 rounded-lg shadow-lg z-50 flex items-center",o.innerHTML=`
        <svg class="h-5 w-5 text-emerald-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">Job submitted successfully! Redirecting to dashboard...</span>
      `,document.body.appendChild(o),setTimeout(()=>{document.body.removeChild(o),e.push("/dashboard")},2e3)}catch(e){d(e instanceof Error?e.message:"Failed to submit job")}finally{r(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[s.jsx("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0 flex items-center",children:s.jsx("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})}),(0,s.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[s.jsx("a",{href:"/dashboard",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),s.jsx("a",{href:"/submit-job",className:"border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Submit Job"}),s.jsx("a",{href:"/buy-hours",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Buy Hours"})]})]}),s.jsx("div",{className:"flex items-center",children:s.jsx("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:s.jsx("span",{children:"Sign Out"})})})]})})}),s.jsx("main",{className:"pt-24 py-10",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[s.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Submit New Job"}),!c&&u.hoursAvailable<=0&&s.jsx("div",{className:"bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"h-5 w-5 text-emerald-500",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[s.jsx("h3",{className:"text-sm font-medium text-emerald-800",children:"No Hours Available"}),s.jsx("div",{className:"mt-2 text-sm text-emerald-700",children:s.jsx("p",{children:"You need to purchase hours before submitting a job. Please buy hours to continue."})}),s.jsx("div",{className:"mt-4",children:s.jsx("a",{href:"/buy-hours",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500",children:"Buy Hours Now"})})]})]})}),i&&s.jsx("div",{className:"bg-red-50 p-4 rounded-md mb-6",children:(0,s.jsxs)("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[s.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Error Submitting Job"}),s.jsx("div",{className:"mt-2 text-sm text-red-700",children:i})]})]})}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-6 bg-white shadow px-6 py-8 rounded-lg",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700",children:"Job Title *"}),s.jsx("input",{type:"text",id:"title",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:h.title,onChange:e=>x({...h,title:e.target.value}),placeholder:"Enter job title"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description *"}),s.jsx("textarea",{id:"description",required:!0,rows:4,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:h.description,onChange:e=>x({...h,description:e.target.value}),placeholder:"Describe the job requirements and details"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"jobType",className:"block text-sm font-medium text-gray-700",children:"Job Type *"}),s.jsx("select",{id:"jobType",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:h.jobType,onChange:e=>x({...h,jobType:e.target.value}),children:Object.values(n.O4).map(e=>s.jsx("option",{value:e,children:l(e)},e))})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700",children:"Category *"}),s.jsx("select",{id:"category",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:h.category,onChange:e=>x({...h,category:e.target.value}),children:Object.values(n.WZ).map(e=>s.jsx("option",{value:e,children:l(e)},e))})]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"outputFormat",className:"block text-sm font-medium text-gray-700",children:"Output Format *"}),s.jsx("select",{id:"outputFormat",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:h.outputFormat,onChange:e=>x({...h,outputFormat:e.target.value}),children:Object.values(n.WJ).map(e=>s.jsx("option",{value:e,children:l(e)},e))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"attachmentUrl",className:"block text-sm font-medium text-gray-700",children:"Attachment URL"}),s.jsx("input",{type:"url",id:"attachmentUrl",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:h.attachmentUrl,onChange:e=>x({...h,attachmentUrl:e.target.value}),placeholder:"https://example.com/attachment.pdf"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"referenceUrl",className:"block text-sm font-medium text-gray-700",children:"Reference URL"}),s.jsx("input",{type:"url",id:"referenceUrl",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:h.referenceUrl,onChange:e=>x({...h,referenceUrl:e.target.value}),placeholder:"https://example.com/reference"})]})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[s.jsx("button",{type:"button",onClick:()=>e.push("/dashboard"),className:"flex-1 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500",children:"Cancel"}),s.jsx("button",{type:"submit",disabled:t||c||u.hoursAvailable<=0,className:"flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed",children:t?"Submitting...":c?"Loading...":u.hoursAvailable<=0?"Buy Hours to Submit Job":"Submit Job"})]})]})]})})})]})}},2630:(e,t,r)=>{"use strict";var s,a,o,n,l;r.d(t,{O4:()=>s,WJ:()=>o,WZ:()=>a}),function(e){e.DataEntry="DataEntry",e.Accounting="Accounting",e.HR="HR",e.ITSupport="ITSupport",e.Marketing="Marketing",e.Legal="Legal",e.CustomerService="CustomerService",e.Other="Other"}(s||(s={})),function(e){e.DataProcessing="DataProcessing",e.DataCleaning="DataCleaning",e.DocumentationEntry="DocumentationEntry",e.Bookkeeping="Bookkeeping",e.FinancialReporting="FinancialReporting",e.Taxation="Taxation",e.Payroll="Payroll",e.Recruitment="Recruitment",e.EmployeeRelations="EmployeeRelations",e.Training="Training",e.CompensationBenefits="CompensationBenefits",e.TechnicalSupport="TechnicalSupport",e.NetworkSupport="NetworkSupport",e.SoftwareSupport="SoftwareSupport",e.HardwareSupport="HardwareSupport",e.DigitalMarketing="DigitalMarketing",e.ContentCreation="ContentCreation",e.SocialMedia="SocialMedia",e.MarketResearch="MarketResearch",e.ContractReview="ContractReview",e.Compliance="Compliance",e.LegalResearch="LegalResearch",e.Documentation="Documentation",e.CallCenter="CallCenter",e.EmailSupport="EmailSupport",e.ChatSupport="ChatSupport",e.CustomerFeedback="CustomerFeedback",e.Other="Other"}(a||(a={})),function(e){e.PDF="PDF",e.Word="Word",e.Excel="Excel",e.PlainText="PlainText",e.JSON="JSON",e.XML="XML",e.Database="Database",e.Other="Other"}(o||(o={})),function(e){e.Pending="Pending",e.UnderReview="UnderReview",e.Assigned="Assigned",e.InProgress="InProgress",e.Completed="Completed",e.Delivered="Delivered",e.Cancelled="Cancelled",e.OnHold="OnHold",e.Rejected="Rejected"}(n||(n={})),function(e){e.User="User",e.Admin="Admin",e.Supervisor="Supervisor",e.Staff="Staff"}(l||(l={}))},2029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>n});var s=r(9510),a=r(5384),o=r.n(a);r(5023);let n={title:"Staff Hall",description:"A modern staffing solution"};function l({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:o().className,children:e})})}},7725:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\submit-job\page.tsx#default`)},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(6621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[948,349,621],()=>r(7656));module.exports=s})();