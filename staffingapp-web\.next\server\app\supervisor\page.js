(()=>{var e={};e.id=511,e.ids=[511],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},6328:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>o}),t(6271),t(2029),t(5866);var a=t(3191),r=t(8716),l=t(7922),i=t.n(l),n=t(5231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o=["",{children:["supervisor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6271)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\supervisor\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\supervisor\\page.tsx"],m="/supervisor/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/supervisor/page",pathname:"/supervisor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6938:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},7286:()=>{},7657:(e,s,t)=>{Promise.resolve().then(t.bind(t,1690))},5047:(e,s,t)=>{"use strict";var a=t(7389);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},1690:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(326),r=t(7577),l=t(5047);let i=e=>e.replace(/([A-Z])/g," $1").trim();function n(){let e=(0,l.useRouter)(),[s,t]=(0,r.useState)(!0),[n,d]=(0,r.useState)(null),[o,c]=(0,r.useState)([]),[m,x]=(0,r.useState)([]),[h,g]=(0,r.useState)("pending"),[p,u]=(0,r.useState)(!1),[f,j]=(0,r.useState)(null),[b,N]=(0,r.useState)({staffId:"",notes:""}),v=async()=>{try{let e=sessionStorage.getItem("token"),s=await fetch("http://localhost:5000/api/jobs/all",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(s.ok){let e=await s.json();c(e)}else console.error("Failed to fetch jobs:",s.status,s.statusText)}catch(e){console.error("Error fetching jobs:",e)}finally{t(!1)}},y=e=>{j(e),u(!0),N({staffId:"",notes:""})},w=async e=>{try{let s=sessionStorage.getItem("token");(await fetch(`http://localhost:5000/api/jobs/${e}/reject`,{method:"PUT",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}})).ok&&v()}catch(e){console.error("Error rejecting job:",e)}},P=async()=>{if(f&&b.staffId)try{let e=sessionStorage.getItem("token");(await fetch(`http://localhost:5000/api/jobs/${f.id}/assign`,{method:"PUT",headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"},body:JSON.stringify({staffId:parseInt(b.staffId),notes:b.notes})})).ok&&(u(!1),j(null),N({staffId:"",notes:""}),v())}catch(e){console.error("Error assigning job:",e)}},S=e=>o.filter(s=>s.status===e),A=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});if(s)return a.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"}),a.jsx("p",{className:"mt-4 text-gray-600",children:"Loading supervisor dashboard..."})]})});let k=S("Pending");S("UnderReview");let C=S("Assigned"),I=S("InProgress");return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[a.jsx("div",{className:"flex items-center",children:a.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"Staff Hall - Supervisor Dashboard"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",n?.firstName," ",n?.lastName]}),a.jsx("button",{onClick:()=>{sessionStorage.removeItem("token"),sessionStorage.removeItem("user"),e.push("/login")},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Logout"})]})]})})}),a.jsx("main",{className:"pt-24 py-10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[a.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsx("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white text-sm font-bold",children:k.length})})}),a.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pending Review"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[k.length," jobs"]})]})})]})})}),a.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsx("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white text-sm font-bold",children:C.length})})}),a.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Assigned"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[C.length," jobs"]})]})})]})})}),a.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsx("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white text-sm font-bold",children:I.length})})}),a.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"In Progress"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[I.length," jobs"]})]})})]})})}),a.jsx("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:a.jsx("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0",children:a.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:a.jsx("span",{className:"text-white text-sm font-bold",children:m.length})})}),a.jsx("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[a.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Staff Members"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[m.length," active"]})]})})]})})})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[a.jsx("div",{className:"border-b border-gray-200",children:a.jsx("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"pending",name:"Pending Review",count:k.length},{id:"assigned",name:"Assigned Jobs",count:C.length},{id:"progress",name:"In Progress",count:I.length},{id:"staff",name:"Staff Management",count:m.length}].map(e=>(0,a.jsxs)("button",{onClick:()=>g(e.id),className:`${h===e.id?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`,children:[e.name,a.jsx("span",{className:"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs",children:e.count})]},e.id))})}),(0,a.jsxs)("div",{className:"p-6",children:["pending"===h&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs Pending Review"}),0===k.length?a.jsx("p",{className:"text-gray-500 text-center py-8",children:"No pending jobs to review"}):a.jsx("div",{className:"space-y-4",children:k.map(s=>a.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),a.jsx("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",i(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",i(s.category)]}),(0,a.jsxs)("span",{children:["Submitted: ",A(s.createdAt)]})]})]}),(0,a.jsxs)("div",{className:"ml-4 flex space-x-2",children:[a.jsx("button",{onClick:()=>e.push(`/jobs/${s.id}`),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),a.jsx("button",{onClick:()=>y(s),className:"bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-1 rounded text-sm",children:"Assign"}),a.jsx("button",{onClick:()=>w(s.id),className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm",children:"Reject"})]})]})},s.id))})]}),"assigned"===h&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Assigned Jobs"}),0===C.length?a.jsx("p",{className:"text-gray-500 text-center py-8",children:"No assigned jobs"}):a.jsx("div",{className:"space-y-4",children:C.map(s=>a.jsx("div",{className:"border border-gray-200 rounded-lg p-4 bg-purple-50",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),a.jsx("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",i(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",i(s.category)]}),(0,a.jsxs)("span",{children:["Assigned: ",s.assignedAt?A(s.assignedAt):"N/A"]})]}),s.assignedTo&&a.jsx("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Assigned to: ",(0,a.jsxs)("strong",{children:[s.assignedTo.firstName," ",s.assignedTo.lastName]})]})}),s.supervisorNotes&&a.jsx("div",{className:"mt-2 p-2 bg-blue-50 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[a.jsx("strong",{children:"Notes:"})," ",s.supervisorNotes]})})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>e.push(`/jobs/${s.id}`),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),a.jsx("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-purple-100 text-purple-800",children:i(s.status)})]})]})},s.id))})]}),"progress"===h&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs In Progress"}),0===I.length?a.jsx("p",{className:"text-gray-500 text-center py-8",children:"No jobs in progress"}):a.jsx("div",{className:"space-y-4",children:I.map(s=>a.jsx("div",{className:"border border-gray-200 rounded-lg p-4 bg-emerald-50",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[a.jsx("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),a.jsx("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",i(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",i(s.category)]}),(0,a.jsxs)("span",{children:["Started: ",s.assignedAt?A(s.assignedAt):"N/A"]})]}),s.assignedTo&&a.jsx("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Being worked on by: ",(0,a.jsxs)("strong",{children:[s.assignedTo.firstName," ",s.assignedTo.lastName]})]})})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center space-x-2",children:[a.jsx("button",{onClick:()=>e.push(`/jobs/${s.id}`),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),a.jsx("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-emerald-100 text-emerald-800",children:i(s.status)})]})]})},s.id))})]}),"staff"===h&&(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Staff Members"}),0===m.length?a.jsx("p",{className:"text-gray-500 text-center py-8",children:"No staff members found"}):a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:m.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),a.jsx("p",{className:"text-gray-600 text-sm",children:e.email}),a.jsx("div",{className:"mt-2",children:a.jsx("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800",children:i(e.role)})})]},e.id))})]})]})]})]})}),p&&f&&a.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:a.jsx("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Assign Job: ",f.title]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Assign to Staff Member"}),(0,a.jsxs)("select",{value:b.staffId,onChange:e=>N({...b,staffId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",required:!0,children:[a.jsx("option",{value:"",children:"Select staff member..."}),m.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.firstName," ",e.lastName," (",e.email,")"]},e.id))]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes for Staff Member (Optional)"}),a.jsx("textarea",{value:b.notes,onChange:e=>N({...b,notes:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",rows:3,placeholder:"Add any special instructions or notes..."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[a.jsx("button",{onClick:()=>{u(!1),j(null),N({staffId:"",notes:""})},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"}),a.jsx("button",{onClick:P,disabled:!b.staffId,className:"bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Assign Job"})]})]})})})]})}},2029:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n,metadata:()=>i});var a=t(9510),r=t(5384),l=t.n(r);t(5023);let i={title:"Staff Hall",description:"A modern staffing solution"};function n({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:l().className,children:e})})}},6271:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\supervisor\page.tsx#default`)},3881:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(6621);let r=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[948,349,621],()=>t(6328));module.exports=a})();