(()=>{var e={};e.id=133,e.ids=[133],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},1916:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>p,pages:()=>o,routeModule:()=>x,tree:()=>d}),s(6684),s(2029),s(5866);var a=s(3191),r=s(8716),l=s(7922),i=s.n(l),n=s(5231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["test-page",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6684)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\test-page\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\test-page\\page.tsx"],p="/test-page/page",h={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/test-page/page",pathname:"/test-page",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6938:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2994,23)),Promise.resolve().then(s.t.bind(s,6114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,9671,23)),Promise.resolve().then(s.t.bind(s,1868,23)),Promise.resolve().then(s.t.bind(s,4759,23))},7286:()=>{},5303:()=>{},2029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,metadata:()=>i});var a=s(9510),r=s(5384),l=s.n(r);s(5023);let i={title:"Staff Hall",description:"A modern staffing solution"};function n({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:l().className,children:e})})}},6684:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(9510);function r(){return(0,a.jsxs)("div",{className:"p-8",children:[a.jsx("h1",{className:"text-4xl font-bold text-red-600 mb-8",children:"\uD83D\uDEA8 TEST PAGE \uD83D\uDEA8"}),(0,a.jsxs)("div",{className:"bg-yellow-200 border-4 border-yellow-600 p-6 rounded-lg mb-8",children:[a.jsx("h2",{className:"text-2xl font-bold text-yellow-800",children:"This is a simple test page"}),a.jsx("p",{className:"text-yellow-700 mt-2",children:"If you can see this, Next.js is working correctly."})]}),(0,a.jsxs)("div",{className:"bg-blue-100 p-6 rounded-lg",children:[a.jsx("h3",{className:"text-xl font-semibold text-blue-800 mb-4",children:"Statement of Account Test"}),a.jsx("div",{className:"bg-white p-4 rounded shadow",children:(0,a.jsxs)("table",{className:"w-full",children:[a.jsx("thead",{children:(0,a.jsxs)("tr",{className:"bg-gray-50",children:[a.jsx("th",{className:"p-2 text-left",children:"Date"}),a.jsx("th",{className:"p-2 text-left",children:"Type"}),a.jsx("th",{className:"p-2 text-left",children:"Reference"}),a.jsx("th",{className:"p-2 text-left",children:"Transaction"}),a.jsx("th",{className:"p-2 text-right",children:"Hours Bought"}),a.jsx("th",{className:"p-2 text-right",children:"Hours Spent"}),a.jsx("th",{className:"p-2 text-right",children:"Balance Hours"}),a.jsx("th",{className:"p-2 text-right",children:"Amount"})]})}),(0,a.jsxs)("tbody",{children:[(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"p-2",children:"2024-01-15"}),a.jsx("td",{className:"p-2",children:"Purchase"}),a.jsx("td",{className:"p-2",children:"INV-001"}),a.jsx("td",{className:"p-2",children:"Hours Purchase"}),a.jsx("td",{className:"p-2 text-right",children:"50.00"}),a.jsx("td",{className:"p-2 text-right",children:"-"}),a.jsx("td",{className:"p-2 text-right",children:"50.00"}),a.jsx("td",{className:"p-2 text-right",children:"$500.00"})]}),(0,a.jsxs)("tr",{className:"bg-gray-50",children:[a.jsx("td",{className:"p-2",children:"2024-01-16"}),a.jsx("td",{className:"p-2",children:"Usage"}),a.jsx("td",{className:"p-2",children:"JOB-001"}),a.jsx("td",{className:"p-2",children:"Web Development"}),a.jsx("td",{className:"p-2 text-right",children:"-"}),a.jsx("td",{className:"p-2 text-right",children:"8.00"}),a.jsx("td",{className:"p-2 text-right",children:"42.00"}),a.jsx("td",{className:"p-2 text-right",children:"-$80.00"})]})]})]})})]})]})}},3881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(6621);let r=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[948,349,621],()=>s(1916));module.exports=a})();