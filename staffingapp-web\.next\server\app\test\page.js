(()=>{var e={};e.id=928,e.ids=[928],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5315:e=>{"use strict";e.exports=require("path")},7360:e=>{"use strict";e.exports=require("url")},1975:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(885),t(2029),t(5866);var r=t(3191),a=t(8716),n=t(7922),i=t.n(n),o=t(5231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let d=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,885)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,2029)),"C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,5866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Documents\\Projects\\staffingapp\\staffingapp-web\\src\\app\\test\\page.tsx"],p="/test/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6938:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2994,23)),Promise.resolve().then(t.t.bind(t,6114,23)),Promise.resolve().then(t.t.bind(t,9727,23)),Promise.resolve().then(t.t.bind(t,9671,23)),Promise.resolve().then(t.t.bind(t,1868,23)),Promise.resolve().then(t.t.bind(t,4759,23))},7286:()=>{},4865:(e,s,t)=>{Promise.resolve().then(t.bind(t,2684))},2684:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(326),a=t(7577);function n(){let[e,s]=(0,a.useState)("");return r.jsx("div",{className:"min-h-screen bg-gray-100 p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-red-500 text-white p-8 rounded-lg mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold mb-4",children:"\uD83D\uDD34 FRONTEND TEST PAGE"}),r.jsx("p",{className:"text-xl",children:"If you can see this, the Next.js frontend is working correctly!"}),e&&(0,r.jsxs)("p",{className:"mt-2",children:["Current time: ",e]})]}),(0,r.jsxs)("div",{className:"bg-blue-500 text-white p-8 rounded-lg mb-8",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"\uD83D\uDD35 Navigation Test"}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("a",{href:"/login",className:"block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded",children:"Go to Login Page"}),r.jsx("a",{href:"/dashboard",className:"block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded",children:"Go to Dashboard Page"}),r.jsx("a",{href:"/submit-job",className:"block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded",children:"Go to Submit Job Page"})]})]}),(0,r.jsxs)("div",{className:"bg-green-500 text-white p-8 rounded-lg",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"\uD83D\uDFE2 Instructions"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-2",children:[r.jsx("li",{children:"If you can see this page, the frontend is working"}),(0,r.jsxs)("li",{children:['Click "Go to Login Page" and log in with: ',r.jsx("strong",{children:"<EMAIL>"})," / ",r.jsx("strong",{children:"password123"})]}),r.jsx("li",{children:'After logging in, click "Go to Dashboard Page"'}),r.jsx("li",{children:"You should see the red test box on the dashboard"})]}),(0,r.jsxs)("div",{className:"mt-4 p-4 bg-green-600 rounded",children:[r.jsx("h3",{className:"font-bold",children:"Correct Login Credentials:"}),(0,r.jsxs)("p",{children:["Email: ",r.jsx("code",{className:"bg-green-700 px-2 py-1 rounded",children:"<EMAIL>"})]}),(0,r.jsxs)("p",{children:["Password: ",r.jsx("code",{className:"bg-green-700 px-2 py-1 rounded",children:"password123"})]})]})]})]})})}},2029:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o,metadata:()=>i});var r=t(9510),a=t(5384),n=t.n(a);t(5023);let i={title:"Staff Hall",description:"A modern staffing solution"};function o({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:n().className,children:e})})}},885:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(8570).createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\test\page.tsx#default`)},3881:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(6621);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[948,349,621],()=>t(1975));module.exports=r})();