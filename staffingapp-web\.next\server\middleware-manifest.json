{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/signup(.json)?[\\/#\\?]?$", "originalSource": "/signup"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "FSfMiGffq1Yqxv233Bw-q", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SkX7ERnQoTzlvwjlgZx8TWs4UBcjyBRMT8oNb69AUzo=", "__NEXT_PREVIEW_MODE_ID": "2298ccf079c8f8e2a8871a822daaeec9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f321c55d589571e33c464539eaa4951bcc15f127723cf0e3f5d65e11ebf3655d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "40f287271caac724cbcd2fa6fa497b1cb26f65663dd620e48721793e744f5ee7"}}}, "functions": {}, "sortedMiddleware": ["/"]}