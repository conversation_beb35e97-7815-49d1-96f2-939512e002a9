(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[665],{1396:function(e,s,o){Promise.resolve().then(o.bind(o,4995))},4995:function(e,s,o){"use strict";o.r(s),o.d(s,{default:function(){return l}});var r=o(7437),a=o(2265),t=o(7648),n=o(9376);function l(){(0,n.useRouter)();let[e,s]=(0,a.useState)({email:"",password:""}),[o,l]=(0,a.useState)(!1),[i,d]=(0,a.useState)(null),c=async s=>{s.preventDefault(),l(!0),d(null),console.log("Login attempt with:",e);try{console.log("Sending request to API...");let s=await fetch("http://localhost:5000/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(console.log("Response status:",s.status),console.log("Response ok:",s.ok),!s.ok){let e=await s.text();throw console.log("Error response:",e),Error("Login failed: ".concat(s.status))}let o=await s.json();console.log("Login successful:",o),sessionStorage.setItem("token",o.token),sessionStorage.setItem("user",JSON.stringify(o.user)),document.cookie="token=".concat(o.token,"; path=/; max-age=86400; samesite=lax");let r=o.user.role,a="/dashboard";"Admin"===r?a="/admin":"Supervisor"===r?a="/supervisor":"Staff"===r&&(a="/staff"),console.log("Redirecting to:",a,"for role:",r),window.location.href=a}catch(e){console.error("Login error:",e),d("Login failed: ".concat(e instanceof Error?e.message:"Unknown error"))}finally{l(!1)}};return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md",children:[(0,r.jsx)("h2",{className:"text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),i&&(0,r.jsx)("div",{className:"bg-red-50 p-4 rounded-md",children:(0,r.jsx)("p",{className:"text-sm text-red-700",children:i})}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:c,children:[(0,r.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e.email,onChange:o=>s({...e,email:o.target.value}),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm",placeholder:"Email address"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,r.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:e.password,onChange:o=>s({...e,password:o.target.value}),className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 focus:z-10 sm:text-sm",placeholder:"Password"})]})]}),(0,r.jsx)("button",{type:"submit",disabled:o,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50",children:o?"Signing in...":"Sign in"})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(t.default,{href:"/signup",className:"text-sm text-emerald-600 hover:text-emerald-500",children:"Don't have an account? Sign up"})})]})})}}},function(e){e.O(0,[448,971,117,744],function(){return e(e.s=1396)}),_N_E=e.O()}]);