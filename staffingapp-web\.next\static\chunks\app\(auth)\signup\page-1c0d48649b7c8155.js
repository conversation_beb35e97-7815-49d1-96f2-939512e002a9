(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[129],{1722:function(e,r,s){Promise.resolve().then(s.bind(s,1514))},1514:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return l}});var a=s(7437),t=s(2265),o=s(9376),n=s(7648);function l(){let e=(0,o.useRouter)(),[r,s]=(0,t.useState)(!1),[l,d]=(0,t.useState)(null),[i,c]=(0,t.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",clientType:"individual",companyName:""}),m=e=>{let{name:r,value:s}=e.target;c(e=>({...e,[r]:s}))},u=async r=>{if(r.preventDefault(),d(null),i.password!==i.confirmPassword){d("Passwords do not match");return}if(i.password.length<6){d("Password must be at least 6 characters long");return}if("corporate"===i.clientType&&!i.companyName.trim()){d("Company name is required for corporate accounts");return}s(!0);try{let r;console.log("Attempting to register user...");let s=await fetch("http://localhost:5000/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:i.firstName,lastName:i.lastName,email:i.email,password:i.password,clientType:i.clientType,companyName:i.companyName})});console.log("Response status:",s.status),console.log("Response headers:",s.headers.get("content-type"));let a=s.headers.get("content-type");if(a&&a.includes("application/json"))try{r=await s.json()}catch(e){throw console.error("Failed to parse JSON:",e),Error("Server returned invalid response format")}else{let e=await s.text();throw console.error("Non-JSON response:",e),Error("Server returned unexpected response format")}if(!s.ok){let e=r;throw Error(e.message||"Registration failed")}d(null);let t=document.createElement("div");t.className="fixed top-4 right-4 bg-emerald-50 border border-emerald-200 text-emerald-800 px-6 py-4 rounded-lg shadow-lg z-50 flex items-center",t.innerHTML='\n        <svg class="h-5 w-5 text-emerald-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>\n        </svg>\n        <span class="font-medium">Account created successfully! Redirecting to login...</span>\n      ',document.body.appendChild(t),setTimeout(()=>{document.body.removeChild(t),e.push("/login")},2e3)}catch(e){console.error("Registration error:",e),d(e instanceof Error?e.message:"Failed to create account")}finally{s(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8 p-8 bg-white rounded-lg shadow-md",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-center text-gray-900",children:"Create Account"}),l&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:l})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:u,children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700",children:"First Name"}),(0,a.jsx)("input",{id:"firstName",name:"firstName",type:"text",required:!0,value:i.firstName,onChange:m,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700",children:"Last Name"}),(0,a.jsx)("input",{id:"lastName",name:"lastName",type:"text",required:!0,value:i.lastName,onChange:m,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"clientType",className:"block text-sm font-medium text-gray-700",children:"Account Type"}),(0,a.jsxs)("select",{id:"clientType",name:"clientType",required:!0,value:i.clientType,onChange:m,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:"individual",children:"Individual Account"}),(0,a.jsx)("option",{value:"corporate",children:"Corporate Account"})]})]}),"corporate"===i.clientType&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"companyName",className:"block text-sm font-medium text-gray-700",children:"Company/Business Name"}),(0,a.jsx)("input",{id:"companyName",name:"companyName",type:"text",required:!0,value:i.companyName,onChange:m,placeholder:"Enter your company or business name",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:i.email,onChange:m,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsx)("input",{id:"password",name:"password",type:"password",required:!0,minLength:6,value:i.password,onChange:m,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,a.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,minLength:6,value:i.confirmPassword,onChange:m,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]})]}),(0,a.jsx)("button",{type:"submit",disabled:r,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50",children:r?"Creating Account...":"Create Account"})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(n.default,{href:"/login",className:"text-sm text-emerald-600 hover:text-emerald-500",children:"Already have an account? Sign in"})})]})})}}},function(e){e.O(0,[448,971,117,744],function(){return e(e.s=1722)}),_N_E=e.O()}]);