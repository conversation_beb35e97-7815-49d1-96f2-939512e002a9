(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3],{2331:function(e,s,t){Promise.resolve().then(t.bind(t,6683))},9376:function(e,s,t){"use strict";var a=t(5475);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},6683:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return o}});var a=t(7437),r=t(2265),i=t(9376),l=t(1981);let n=e=>e.replace(/([A-Z])/g," $1").trim(),d={[l.O4.DataEntry]:[l.WZ.DataProcessing,l.WZ.DataCleaning,l.WZ.DocumentationEntry],[l.O4.Accounting]:[l.WZ.Bookkeeping,l.WZ.FinancialReporting,l.WZ.Taxation,l.WZ.Payroll],[l.O4.HR]:[l.WZ.Recruitment,l.WZ.EmployeeRelations,l.WZ.Training,l.WZ.CompensationBenefits],[l.O4.ITSupport]:[l.WZ.TechnicalSupport,l.WZ.NetworkSupport,l.WZ.SoftwareSupport,l.WZ.HardwareSupport],[l.O4.Marketing]:[l.WZ.DigitalMarketing,l.WZ.ContentCreation,l.WZ.SocialMedia,l.WZ.MarketResearch],[l.O4.Legal]:[l.WZ.ContractReview,l.WZ.Compliance,l.WZ.LegalResearch,l.WZ.Documentation],[l.O4.CustomerService]:[l.WZ.CallCenter,l.WZ.EmailSupport,l.WZ.ChatSupport,l.WZ.CustomerFeedback],[l.O4.Other]:[l.WZ.Other]},c=e=>{for(let[s,t]of Object.entries(d))if(t.includes(e))return s;return l.O4.Other};function o(){let e=(0,i.useRouter)(),[s,t]=(0,r.useState)(!0),[l,n]=(0,r.useState)("overview"),[d,c]=(0,r.useState)(null);return((0,r.useEffect)(()=>{(()=>{let s=sessionStorage.getItem("token"),a=sessionStorage.getItem("user");if(!s||!a){e.push("/login");return}c(JSON.parse(a)),t(!1)})()},[e]),s)?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex justify-between h-16",children:(0,a.jsx)("div",{className:"flex",children:(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall Admin"})})})})})}),(0,a.jsx)("main",{className:"pt-24 py-10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading admin dashboard..."})]})})})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex",children:(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall Admin"})})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Welcome, ",null==d?void 0:d.firstName]}),(0,a.jsx)("a",{href:"/dashboard",className:"text-emerald-600 hover:text-emerald-800 text-sm font-medium",children:"User Dashboard"}),(0,a.jsx)("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:(0,a.jsx)("span",{children:"Sign Out"})})]})]})})}),(0,a.jsx)("main",{className:"pt-24 py-10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Manage your Staff Hall application settings and configurations"})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",name:"Overview",icon:"\uD83D\uDCCA"},{id:"job-types",name:"Job Types",icon:"\uD83D\uDCDD"},{id:"categories",name:"Categories",icon:"\uD83D\uDCC2"},{id:"rates",name:"Hour Rates",icon:"\uD83D\uDCB0"},{id:"bonuses",name:"Bonus Limits",icon:"\uD83C\uDF81"},{id:"users",name:"User Management",icon:"\uD83D\uDC65"},{id:"settings",name:"Settings",icon:"⚙️"}].map(e=>(0,a.jsxs)("button",{onClick:()=>n(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ".concat(l===e.id?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,a.jsx)("span",{className:"mr-2",children:e.icon}),e.name]},e.id))})})}),(0,a.jsx)("div",{className:"bg-white shadow rounded-lg",children:(()=>{switch(l){case"overview":default:return(0,a.jsx)(m,{});case"job-types":return(0,a.jsx)(x,{});case"categories":return(0,a.jsx)(u,{});case"rates":return(0,a.jsx)(p,{});case"bonuses":return(0,a.jsx)(h,{});case"users":return(0,a.jsx)(g,{});case"settings":return(0,a.jsx)(b,{})}})()})]})})]})}function m(){return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"System Overview"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:(0,a.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDC65"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Total Users"}),(0,a.jsx)("div",{className:"text-3xl font-bold text-emerald-900",children:"156"})]})]})}),(0,a.jsx)("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:(0,a.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDCDD"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Active Jobs"}),(0,a.jsx)("div",{className:"text-3xl font-bold text-emerald-900",children:"42"})]})]})}),(0,a.jsx)("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:(0,a.jsx)("span",{className:"text-white text-xl",children:"\uD83D\uDCB0"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Revenue"}),(0,a.jsx)("div",{className:"text-3xl font-bold text-emerald-900",children:"$12,450"})]})]})}),(0,a.jsx)("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:(0,a.jsx)("span",{className:"text-white text-xl",children:"⏱️"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Hours Sold"}),(0,a.jsx)("div",{className:"text-3xl font-bold text-emerald-900",children:"2,340"})]})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Activity"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("span",{className:"text-white text-sm",children:"\uD83D\uDC64"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"New user registered"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"John Doe joined as Individual account"})]}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"2 minutes ago"})]}),(0,a.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("span",{className:"text-white text-sm",children:"\uD83D\uDCDD"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Job completed"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Data Entry task finished by ABC Corp"})]}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"15 minutes ago"})]}),(0,a.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)("span",{className:"text-white text-sm",children:"\uD83D\uDCB0"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Hours purchased"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Jane Smith bought 50 hours"})]}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"1 hour ago"})]})]})]})]})}function x(){let[e,s]=(0,r.useState)(()=>Object.values(l.O4).map((e,s)=>({id:s+1,name:e,description:"".concat(n(e)," tasks and services"),isActive:!0}))),[t,i]=(0,r.useState)(!1),[d,c]=(0,r.useState)(null),[o,m]=(0,r.useState)({name:l.O4.DataEntry,description:"",isActive:!0}),x=e=>{c(e),m({name:e.name,description:e.description,isActive:e.isActive}),i(!0)},u=t=>{confirm("Are you sure you want to delete this job type?")&&s(e.filter(e=>e.id!==t))},p=t=>{s(e.map(e=>e.id===t?{...e,isActive:!e.isActive}:e))};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Job Types Management"}),(0,a.jsx)("button",{onClick:()=>{i(!0),c(null),m({name:l.O4.DataEntry,description:"",isActive:!0})},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Add Job Type"})]}),t&&(0,a.jsxs)("div",{className:"mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-md font-medium text-emerald-800 mb-4",children:d?"Edit Job Type":"Add New Job Type"}),(0,a.jsxs)("form",{onSubmit:t=>{if(t.preventDefault(),d)s(e.map(e=>e.id===d.id?{...e,...o}:e)),c(null);else{let t={id:Math.max(...e.map(e=>e.id))+1,...o};s([...e,t])}m({name:l.O4.DataEntry,description:"",isActive:!0}),i(!1)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Job Type Name"}),(0,a.jsxs)("select",{required:!0,value:o.name,onChange:e=>m({...o,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:l.O4.DataEntry,children:"Select a job type..."}),Object.values(l.O4).map(e=>(0,a.jsx)("option",{value:e,children:n(e)},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:o.isActive.toString(),onChange:e=>m({...o,isActive:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:"true",children:"Active"}),(0,a.jsx)("option",{value:"false",children:"Inactive"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,a.jsx)("textarea",{required:!0,value:o.description,onChange:e=>m({...o,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",rows:3,placeholder:"Describe this job type..."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{type:"submit",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:[d?"Update":"Add"," Job Type"]}),(0,a.jsx)("button",{type:"button",onClick:()=>{i(!1),c(null),m({name:l.O4.DataEntry,description:"",isActive:!0})},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]})]}),(0,a.jsx)("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:n(e.name)}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.description}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("button",{onClick:()=>p(e.id),className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-emerald-100 text-emerald-800":"bg-gray-100 text-gray-800"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,a.jsx)("button",{onClick:()=>x(e),className:"text-emerald-600 hover:text-emerald-900",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>u(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})})]})}function u(){let[e,s]=(0,r.useState)(()=>Object.values(l.WZ).map((e,s)=>({id:s+1,name:e,jobType:c(e),description:"".concat(n(e)," tasks and services"),isActive:!0}))),[t,i]=(0,r.useState)(!1),[d,o]=(0,r.useState)(null),[m,x]=(0,r.useState)({name:l.WZ.DataProcessing,jobType:l.O4.DataEntry,description:"",isActive:!0}),u=e=>{o(e),x({name:e.name,jobType:e.jobType,description:e.description,isActive:e.isActive}),i(!0)},p=t=>{confirm("Are you sure you want to delete this category?")&&s(e.filter(e=>e.id!==t))},h=t=>{s(e.map(e=>e.id===t?{...e,isActive:!e.isActive}:e))};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Categories Management"}),(0,a.jsx)("button",{onClick:()=>{i(!0),o(null),x({name:l.WZ.DataProcessing,jobType:l.O4.DataEntry,description:"",isActive:!0})},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Add Category"})]}),t&&(0,a.jsxs)("div",{className:"mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-md font-medium text-emerald-800 mb-4",children:d?"Edit Category":"Add New Category"}),(0,a.jsxs)("form",{onSubmit:t=>{if(t.preventDefault(),d)s(e.map(e=>e.id===d.id?{...e,...m}:e)),o(null);else{let t={id:Math.max(...e.map(e=>e.id))+1,...m};s([...e,t])}x({name:l.WZ.DataProcessing,jobType:l.O4.DataEntry,description:"",isActive:!0}),i(!1)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category Name"}),(0,a.jsxs)("select",{required:!0,value:m.name,onChange:e=>x({...m,name:e.target.value,jobType:c(e.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:l.WZ.DataProcessing,children:"Select a category..."}),Object.values(l.WZ).map(e=>(0,a.jsx)("option",{value:e,children:n(e)},e))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Job Type"}),(0,a.jsx)("input",{type:"text",value:n(m.jobType),readOnly:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600",placeholder:"Auto-assigned based on category"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:m.isActive.toString(),onChange:e=>x({...m,isActive:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:"true",children:"Active"}),(0,a.jsx)("option",{value:"false",children:"Inactive"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,a.jsx)("textarea",{required:!0,value:m.description,onChange:e=>x({...m,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",rows:3,placeholder:"Describe this category..."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{type:"submit",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:[d?"Update":"Add"," Category"]}),(0,a.jsx)("button",{type:"button",onClick:()=>{i(!1),o(null),x({name:l.WZ.DataProcessing,jobType:l.O4.DataEntry,description:"",isActive:!0})},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]})]}),(0,a.jsx)("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Job Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:n(e.name)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-600",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800",children:n(e.jobType)})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.description}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("button",{onClick:()=>h(e.id),className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-emerald-100 text-emerald-800":"bg-gray-100 text-gray-800"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,a.jsx)("button",{onClick:()=>u(e),className:"text-emerald-600 hover:text-emerald-900",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>p(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})})]})}function p(){let[e,s]=(0,r.useState)([{id:1,packageName:"Basic Package",hours:10,price:50,pricePerHour:5,isActive:!0},{id:2,packageName:"Standard Package",hours:25,price:100,pricePerHour:4,isActive:!0},{id:3,packageName:"Premium Package",hours:50,price:180,pricePerHour:3.6,isActive:!0},{id:4,packageName:"Enterprise Package",hours:100,price:320,pricePerHour:3.2,isActive:!0}]),[t,i]=(0,r.useState)(!1),[l,n]=(0,r.useState)(null),[d,c]=(0,r.useState)({packageName:"",hours:0,price:0,isActive:!0}),o=(e,s)=>s>0?(e/s).toFixed(2):"0.00",m=e=>{n(e),c({packageName:e.packageName,hours:e.hours,price:e.price,isActive:e.isActive}),i(!0)},x=t=>{confirm("Are you sure you want to delete this rate package?")&&s(e.filter(e=>e.id!==t))},u=t=>{s(e.map(e=>e.id===t?{...e,isActive:!e.isActive}:e))};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Hour Rates Management"}),(0,a.jsx)("button",{onClick:()=>{i(!0),n(null),c({packageName:"",hours:0,price:0,isActive:!0})},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Add Rate Package"})]}),t&&(0,a.jsxs)("div",{className:"mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-md font-medium text-emerald-800 mb-4",children:l?"Edit Rate Package":"Add New Rate Package"}),(0,a.jsxs)("form",{onSubmit:t=>{t.preventDefault();let a=parseFloat(o(d.price,d.hours));if(l)s(e.map(e=>e.id===l.id?{...e,...d,pricePerHour:a}:e)),n(null);else{let t={id:Math.max(...e.map(e=>e.id))+1,...d,pricePerHour:a};s([...e,t])}c({packageName:"",hours:0,price:0,isActive:!0}),i(!1)},className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Package Name"}),(0,a.jsx)("input",{type:"text",required:!0,value:d.packageName,onChange:e=>c({...d,packageName:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",placeholder:"e.g., Basic Package"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Hours"}),(0,a.jsx)("input",{type:"number",required:!0,min:"1",value:d.hours||"",onChange:e=>c({...d,hours:parseInt(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",placeholder:"10"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price ($)"}),(0,a.jsx)("input",{type:"number",required:!0,min:"0",step:"0.01",value:d.price||"",onChange:e=>c({...d,price:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",placeholder:"50.00"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:d.isActive.toString(),onChange:e=>c({...d,isActive:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:"true",children:"Active"}),(0,a.jsx)("option",{value:"false",children:"Inactive"})]})]})]}),d.hours>0&&d.price>0&&(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Price per hour: ",(0,a.jsxs)("span",{className:"font-medium",children:["$",o(d.price,d.hours)]})]})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("button",{type:"submit",className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:[l?"Update":"Add"," Rate Package"]}),(0,a.jsx)("button",{type:"button",onClick:()=>{i(!1),n(null),c({packageName:"",hours:0,price:0,isActive:!0})},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]})]}),(0,a.jsx)("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Package Name"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Hours"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Price/Hour"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.packageName}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.hours}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["$",e.price.toFixed(2)]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:["$",e.pricePerHour.toFixed(2)]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("button",{onClick:()=>u(e.id),className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-emerald-100 text-emerald-800":"bg-gray-100 text-gray-800"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,a.jsx)("button",{onClick:()=>m(e),className:"text-emerald-600 hover:text-emerald-900",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>x(e.id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})]},e.id))})]})})]})}function h(){let[e,s]=(0,r.useState)({individual:{enabled:!0,minPurchase:50,bonusPercentage:10,maxBonusAmount:20,description:"Get 10% bonus hours on purchases over $50"},corporate:{enabled:!0,minPurchase:200,bonusPercentage:15,maxBonusAmount:100,description:"Get 15% bonus hours on purchases over $200"}}),[t,i]=(0,r.useState)(null),[l,n]=(0,r.useState)({enabled:!0,minPurchase:0,bonusPercentage:0,maxBonusAmount:0,description:""}),d=s=>{i(s),n(e[s])},c=()=>{t&&(s({...e,[t]:l}),i(null))},o=()=>{i(null),n({enabled:!0,minPurchase:0,bonusPercentage:0,maxBonusAmount:0,description:""})};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-2",children:"Bonus Limits Management"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Configure bonus hour settings for individual and corporate accounts"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-emerald-50 border border-emerald-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-emerald-800",children:"Individual Account Bonuses"}),(0,a.jsx)("p",{className:"text-sm text-emerald-600 mt-1",children:"Bonus settings for individual user accounts"})]}),(0,a.jsx)("button",{onClick:()=>d("individual"),className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Edit Settings"})]}),"individual"===t?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:l.enabled.toString(),onChange:e=>n({...l,enabled:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:"true",children:"Enabled"}),(0,a.jsx)("option",{value:"false",children:"Disabled"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Purchase ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:l.minPurchase,onChange:e=>n({...l,minPurchase:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bonus Percentage (%)"}),(0,a.jsx)("input",{type:"number",min:"0",max:"100",value:l.bonusPercentage,onChange:e=>n({...l,bonusPercentage:parseInt(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Bonus ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:l.maxBonusAmount,onChange:e=>n({...l,maxBonusAmount:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:l.description,onChange:e=>n({...l,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",rows:2,placeholder:"Describe the bonus offer..."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:c,className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Save Changes"}),(0,a.jsx)("button",{onClick:o,className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Status:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:e.individual.enabled?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Min Purchase:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:["$",e.individual.minPurchase]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Bonus Percentage:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:[e.individual.bonusPercentage,"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Max Bonus:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:["$",e.individual.maxBonusAmount]})]}),(0,a.jsxs)("div",{className:"md:col-span-2 lg:col-span-4",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Description:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:e.individual.description})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-blue-800",children:"Corporate Account Bonuses"}),(0,a.jsx)("p",{className:"text-sm text-blue-600 mt-1",children:"Bonus settings for corporate user accounts"})]}),(0,a.jsx)("button",{onClick:()=>d("corporate"),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Edit Settings"})]}),"corporate"===t?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),(0,a.jsxs)("select",{value:l.enabled.toString(),onChange:e=>n({...l,enabled:"true"===e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"true",children:"Enabled"}),(0,a.jsx)("option",{value:"false",children:"Disabled"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Min Purchase ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:l.minPurchase,onChange:e=>n({...l,minPurchase:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bonus Percentage (%)"}),(0,a.jsx)("input",{type:"number",min:"0",max:"100",value:l.bonusPercentage,onChange:e=>n({...l,bonusPercentage:parseInt(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Max Bonus ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:l.maxBonusAmount,onChange:e=>n({...l,maxBonusAmount:parseFloat(e.target.value)||0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:l.description,onChange:e=>n({...l,description:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",rows:2,placeholder:"Describe the bonus offer..."})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:c,className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Save Changes"}),(0,a.jsx)("button",{onClick:o,className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"})]})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Status:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:e.corporate.enabled?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Min Purchase:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:["$",e.corporate.minPurchase]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Bonus Percentage:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:[e.corporate.bonusPercentage,"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Max Bonus:"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900",children:["$",e.corporate.maxBonusAmount]})]}),(0,a.jsxs)("div",{className:"md:col-span-2 lg:col-span-4",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Description:"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:e.corporate.description})]})]})]})]})]})}function g(){let[e,s]=(0,r.useState)([{id:1,firstName:"John",lastName:"Doe",email:"<EMAIL>",clientType:"Individual",companyName:null,role:"user",isActive:!0,createdAt:"2024-01-15",hoursBought:50,hoursUsed:25},{id:2,firstName:"Jane",lastName:"Smith",email:"<EMAIL>",clientType:"Corporate",companyName:"ABC Corp",role:"user",isActive:!0,createdAt:"2024-01-10",hoursBought:200,hoursUsed:150},{id:3,firstName:"Admin",lastName:"User",email:"<EMAIL>",clientType:"Individual",companyName:null,role:"admin",isActive:!0,createdAt:"2024-01-01",hoursBought:0,hoursUsed:0}]),[t,i]=(0,r.useState)(""),[l,n]=(0,r.useState)("all"),[d,c]=(0,r.useState)("all"),o=e.filter(e=>{let s=e.firstName.toLowerCase().includes(t.toLowerCase())||e.lastName.toLowerCase().includes(t.toLowerCase())||e.email.toLowerCase().includes(t.toLowerCase())||e.companyName&&e.companyName.toLowerCase().includes(t.toLowerCase()),a="all"===l||e.clientType.toLowerCase()===l.toLowerCase(),r="all"===d||e.role===d;return s&&a&&r}),m=t=>{confirm("Are you sure you want to change this user's status?")&&s(e.map(e=>e.id===t?{...e,isActive:!e.isActive}:e))},x=(t,a)=>{confirm("Are you sure you want to change this user's role to ".concat(a,"?"))&&s(e.map(e=>e.id===t?{...e,role:a}:e))};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-4",children:"User Management"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Users"}),(0,a.jsx)("input",{type:"text",value:t,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",placeholder:"Search by name, email, or company..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Account Type"}),(0,a.jsxs)("select",{value:l,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"individual",children:"Individual"}),(0,a.jsx)("option",{value:"corporate",children:"Corporate"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Role"}),(0,a.jsxs)("select",{value:d,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:"all",children:"All Roles"}),(0,a.jsx)("option",{value:"user",children:"User"}),(0,a.jsx)("option",{value:"admin",children:"Admin"})]})]}),(0,a.jsx)("div",{className:"flex items-end",children:(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",o.length," of ",e.length," users"]})})]})]}),(0,a.jsx)("div",{className:"overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-300",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Account Type"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Hours"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Joined"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email}),e.companyName&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.companyName})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("Corporate"===e.clientType?"bg-blue-100 text-blue-800":"bg-emerald-100 text-emerald-800"),children:e.clientType})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("select",{value:e.role,onChange:s=>x(e.id,s.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",children:[(0,a.jsx)("option",{value:"user",children:"User"}),(0,a.jsx)("option",{value:"admin",children:"Admin"})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[(0,a.jsxs)("div",{children:["Bought: ",e.hoursBought]}),(0,a.jsxs)("div",{children:["Used: ",e.hoursUsed]}),(0,a.jsxs)("div",{children:["Available: ",e.hoursBought-e.hoursUsed]})]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("button",{onClick:()=>m(e.id),className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-emerald-100 text-emerald-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,a.jsx)("button",{className:"text-emerald-600 hover:text-emerald-900",children:"View Details"}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-900",children:"Edit Hours"})]})]},e.id))})]})}),0===o.length&&(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No users found matching your criteria."})})]})}function b(){let[e,s]=(0,r.useState)({general:{appName:"Staff Hall",supportEmail:"<EMAIL>",maintenanceMode:!1,allowRegistration:!0},notifications:{emailNotifications:!0,jobCompletionNotifications:!0,paymentNotifications:!0,systemAlerts:!0},security:{sessionTimeout:30,passwordMinLength:8,requireTwoFactor:!1,maxLoginAttempts:5}}),[t,i]=(0,r.useState)("general"),l=(t,a,r)=>{s({...e,[t]:{...e[t],[a]:r}})};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-2",children:"System Settings"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Configure application-wide settings and preferences"})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"general",name:"General",icon:"⚙️"},{id:"notifications",name:"Notifications",icon:"\uD83D\uDD14"},{id:"security",name:"Security",icon:"\uD83D\uDD12"}].map(e=>(0,a.jsxs)("button",{onClick:()=>i(e.id),className:"flex items-center px-3 py-2 text-sm font-medium rounded-md ".concat(t===e.id?"bg-emerald-100 text-emerald-700":"text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)("span",{className:"mr-2",children:e.icon}),e.name]},e.id))})}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6",children:["general"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"General Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Application Name"}),(0,a.jsx)("input",{type:"text",value:e.general.appName,onChange:e=>l("general","appName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Support Email"}),(0,a.jsx)("input",{type:"email",value:e.general.supportEmail,onChange:e=>l("general","supportEmail",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"maintenanceMode",checked:e.general.maintenanceMode,onChange:e=>l("general","maintenanceMode",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"maintenanceMode",className:"ml-2 block text-sm text-gray-900",children:"Enable Maintenance Mode"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"allowRegistration",checked:e.general.allowRegistration,onChange:e=>l("general","allowRegistration",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"allowRegistration",className:"ml-2 block text-sm text-gray-900",children:"Allow New User Registration"})]})]})]}),"notifications"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Notification Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"emailNotifications",checked:e.notifications.emailNotifications,onChange:e=>l("notifications","emailNotifications",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"emailNotifications",className:"ml-2 block text-sm text-gray-900",children:"Enable Email Notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"jobCompletionNotifications",checked:e.notifications.jobCompletionNotifications,onChange:e=>l("notifications","jobCompletionNotifications",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"jobCompletionNotifications",className:"ml-2 block text-sm text-gray-900",children:"Job Completion Notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"paymentNotifications",checked:e.notifications.paymentNotifications,onChange:e=>l("notifications","paymentNotifications",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"paymentNotifications",className:"ml-2 block text-sm text-gray-900",children:"Payment Notifications"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"systemAlerts",checked:e.notifications.systemAlerts,onChange:e=>l("notifications","systemAlerts",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"systemAlerts",className:"ml-2 block text-sm text-gray-900",children:"System Alerts"})]})]})]}),"security"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Security Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Session Timeout (minutes)"}),(0,a.jsx)("input",{type:"number",min:"5",max:"120",value:e.security.sessionTimeout,onChange:e=>l("security","sessionTimeout",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password Minimum Length"}),(0,a.jsx)("input",{type:"number",min:"6",max:"20",value:e.security.passwordMinLength,onChange:e=>l("security","passwordMinLength",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Max Login Attempts"}),(0,a.jsx)("input",{type:"number",min:"3",max:"10",value:e.security.maxLoginAttempts,onChange:e=>l("security","maxLoginAttempts",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"requireTwoFactor",checked:e.security.requireTwoFactor,onChange:e=>l("security","requireTwoFactor",e.target.checked),className:"h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"requireTwoFactor",className:"ml-2 block text-sm text-gray-900",children:"Require Two-Factor Authentication"})]})]}),(0,a.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,a.jsx)("button",{onClick:()=>{alert("Settings saved successfully!")},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-md text-sm font-medium",children:"Save Settings"})})]})]})}},1981:function(e,s,t){"use strict";var a,r,i,l,n,d,c,o,m,x;t.d(s,{O4:function(){return a},WJ:function(){return i},WZ:function(){return r}}),(d=a||(a={})).DataEntry="DataEntry",d.Accounting="Accounting",d.HR="HR",d.ITSupport="ITSupport",d.Marketing="Marketing",d.Legal="Legal",d.CustomerService="CustomerService",d.Other="Other",(c=r||(r={})).DataProcessing="DataProcessing",c.DataCleaning="DataCleaning",c.DocumentationEntry="DocumentationEntry",c.Bookkeeping="Bookkeeping",c.FinancialReporting="FinancialReporting",c.Taxation="Taxation",c.Payroll="Payroll",c.Recruitment="Recruitment",c.EmployeeRelations="EmployeeRelations",c.Training="Training",c.CompensationBenefits="CompensationBenefits",c.TechnicalSupport="TechnicalSupport",c.NetworkSupport="NetworkSupport",c.SoftwareSupport="SoftwareSupport",c.HardwareSupport="HardwareSupport",c.DigitalMarketing="DigitalMarketing",c.ContentCreation="ContentCreation",c.SocialMedia="SocialMedia",c.MarketResearch="MarketResearch",c.ContractReview="ContractReview",c.Compliance="Compliance",c.LegalResearch="LegalResearch",c.Documentation="Documentation",c.CallCenter="CallCenter",c.EmailSupport="EmailSupport",c.ChatSupport="ChatSupport",c.CustomerFeedback="CustomerFeedback",c.Other="Other",(o=i||(i={})).PDF="PDF",o.Word="Word",o.Excel="Excel",o.PlainText="PlainText",o.JSON="JSON",o.XML="XML",o.Database="Database",o.Other="Other",(m=l||(l={})).Pending="Pending",m.UnderReview="UnderReview",m.Assigned="Assigned",m.InProgress="InProgress",m.Completed="Completed",m.Delivered="Delivered",m.Cancelled="Cancelled",m.OnHold="OnHold",m.Rejected="Rejected",(x=n||(n={})).User="User",x.Admin="Admin",x.Supervisor="Supervisor",x.Staff="Staff"}},function(e){e.O(0,[971,117,744],function(){return e(e.s=2331)}),_N_E=e.O()}]);