(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[340],{8720:function(e,r,s){Promise.resolve().then(s.bind(s,5304))},9376:function(e,r,s){"use strict";var t=s(5475);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}})},5304:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return o}});var t=s(7437),a=s(2265),l=s(9376);function o(){let e=(0,l.useRouter)(),[r,s]=(0,a.useState)(!1),[o,n]=(0,a.useState)(null),[i,d]=(0,a.useState)(null),[c,m]=(0,a.useState)(null),[u,x]=(0,a.useState)({hours:1,paymentReference:"",paymentProof:""}),[h,f]=(0,a.useState)({file:null,uploading:!1,uploadedUrl:null});(0,a.useEffect)(()=>{if(!sessionStorage.getItem("token")){e.push("/login");return}p()},[e]);let p=async()=>{try{let e=sessionStorage.getItem("token");if(!e)return;let r=await fetch("http://localhost:5000/api/hours/rates/current",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(r.ok){let e=await r.json();m(e)}else m({id:1,ratePerHour:25,currency:"USD",isActive:!0})}catch(e){console.error("Error fetching rate:",e),m({id:1,ratePerHour:25,currency:"USD",isActive:!0})}},g=async()=>{if(!h.file)throw Error("No file selected");f(e=>({...e,uploading:!0}));try{let e=sessionStorage.getItem("token");if(!e)throw Error("Authentication required");let r=new FormData;r.append("file",h.file),r.append("type","payment-proof");let s=await fetch("http://localhost:5000/api/attachments/upload",{method:"POST",headers:{Authorization:"Bearer ".concat(e)},body:r});if(!s.ok)throw Error("Failed to upload file");let t=await s.json(),a=t.filePath||t.url||"uploads/".concat(h.file.name);return f(e=>({...e,uploading:!1,uploadedUrl:a})),a}catch(e){throw f(e=>({...e,uploading:!1})),e}},j=async r=>{r.preventDefault(),s(!0),n(null),d(null);try{let r;let s=sessionStorage.getItem("token");if(!s)throw Error("Authentication required");if(!h.file&&!h.uploadedUrl)throw Error("Please select a payment proof file");let t=h.uploadedUrl;h.file&&!t&&(t=await g());let a={...u,paymentProof:t},l=await fetch("http://localhost:5000/api/HoursPurchase/purchase",{method:"POST",headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"},body:JSON.stringify(a)});if(!l.ok){console.error("Purchase API Error - Status:",l.status),console.error("Purchase API Error - Status Text:",l.statusText);let e="Failed to submit purchase request";try{e=(await l.json()).message||e}catch(s){console.error("Failed to parse error response as JSON:",s);let r=await l.text();console.error("Error response text:",r),e="Server error (".concat(l.status,"): ").concat(r||l.statusText)}throw Error(e)}try{r=await l.json()}catch(r){console.error("Failed to parse success response as JSON:",r);let e=await l.text();throw console.error("Success response text:",e),Error("Server returned invalid response format")}d("Purchase request submitted successfully! Reference ID: ".concat(r.id)),x({hours:1,paymentReference:"",paymentProof:""}),f({file:null,uploading:!1,uploadedUrl:null}),setTimeout(()=>{e.push("/dashboard")},2e3)}catch(e){console.error("Purchase error:",e),n(e instanceof Error?e.message:"Failed to submit purchase request")}finally{s(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,t.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between h-16",children:[(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,t.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})}),(0,t.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[(0,t.jsx)("a",{href:"/dashboard",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),(0,t.jsx)("a",{href:"/submit-job",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Submit Job"}),(0,t.jsx)("a",{href:"/buy-hours",className:"border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Buy Hours"})]})]}),(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsx)("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:(0,t.jsx)("span",{children:"Sign Out"})})})]})})}),(0,t.jsx)("main",{className:"pt-24 py-10",children:(0,t.jsx)("div",{className:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,t.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Buy Hours"}),c&&(0,t.jsx)("div",{className:"bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-emerald-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-emerald-800",children:"Current Rate"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-emerald-700",children:(0,t.jsxs)("p",{children:["$",c.ratePerHour.toFixed(2)," ",c.currency," per hour"]})})]})]})}),o&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,t.jsx)("p",{children:o})})]})]})}),i&&(0,t.jsx)("div",{className:"bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-emerald-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-emerald-800",children:"Success"}),(0,t.jsxs)("div",{className:"mt-2 text-sm text-emerald-700",children:[(0,t.jsx)("p",{children:i}),(0,t.jsx)("p",{className:"mt-1 text-emerald-600",children:"Redirecting to dashboard in 2 seconds..."})]})]})]})}),(0,t.jsxs)("form",{onSubmit:j,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"hours",className:"block text-sm font-medium text-gray-700",children:"Number of Hours"}),(0,t.jsx)("input",{type:"number",id:"hours",min:"1",max:"1000",required:!0,value:u.hours,onChange:e=>x({...u,hours:parseInt(e.target.value)||1}),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm"})]}),c&&(0,t.jsx)("div",{className:"bg-gray-50 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Total Cost:"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-gray-900",children:["$",(c?u.hours*c.ratePerHour:0).toFixed(2)," ",c.currency]})]})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"paymentReference",className:"block text-sm font-medium text-gray-700",children:"Payment Reference"}),(0,t.jsx)("input",{type:"text",id:"paymentReference",required:!0,placeholder:"e.g., Bank transfer reference, PayPal transaction ID",value:u.paymentReference,onChange:e=>x({...u,paymentReference:e.target.value}),className:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 sm:text-sm"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"paymentProof",className:"block text-sm font-medium text-gray-700",children:"Payment Proof"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)("input",{type:"file",id:"paymentProof",accept:"image/*,.pdf",required:!0,onChange:e=>{var r;let s=null===(r=e.target.files)||void 0===r?void 0:r[0];if(s){if(!["image/jpeg","image/jpg","image/png","image/gif","application/pdf"].includes(s.type)){n("Please upload an image (JPG, PNG, GIF) or PDF file");return}if(s.size>5242880){n("File size must be less than 5MB");return}f({file:s,uploading:!1,uploadedUrl:null}),n(null)}},className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-emerald-50 file:text-emerald-700 hover:file:bg-emerald-100"})}),h.file&&(0,t.jsx)("div",{className:"mt-2 text-sm",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"h-4 w-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,t.jsxs)("span",{className:"text-green-700",children:["Selected: ",h.file.name," (",(h.file.size/1024/1024).toFixed(2)," MB)"]})]})}),h.uploading&&(0,t.jsx)("div",{className:"mt-2 text-sm text-emerald-600",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("svg",{className:"animate-spin h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,t.jsx)("span",{children:"Uploading file..."})]})}),h.uploadedUrl&&(0,t.jsx)("div",{className:"mt-2 text-sm text-emerald-600",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})}),(0,t.jsx)("span",{children:"File uploaded successfully"})]})}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Upload a screenshot or document proving your payment (JPG, PNG, GIF, or PDF, max 5MB)"})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{type:"button",onClick:()=>e.push("/dashboard"),className:"bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:r||h.uploading||!h.file&&!h.uploadedUrl,className:"bg-emerald-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50",children:h.uploading?"Uploading File...":r?"Submitting...":"Submit Purchase Request"})]})]}),(0,t.jsx)("div",{className:"mt-8 bg-yellow-50 border border-yellow-200 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Payment Instructions"}),(0,t.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:(0,t.jsxs)("p",{children:["1. Calculate the total cost based on the current rate",(0,t.jsx)("br",{}),"2. Make the payment via your preferred method",(0,t.jsx)("br",{}),"3. Fill out this form with payment details",(0,t.jsx)("br",{}),"4. Your request will be reviewed and hours will be added to your account"]})})]})]})})]})})})})]})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=8720)}),_N_E=e.O()}]);