(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{5858:function(e,t,s){Promise.resolve().then(s.bind(s,6872))},6872:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return c}});var r=s(7437),o=s(2265),i=s(9376),n=s(7567),l=s(9740);let a=e=>e.replace(/([A-Z])/g," $1").trim();function d(e){let{onError:t,hoursStatistics:s}=e,n=(0,i.useRouter)(),[d,c]=(0,o.useState)([]),[x,h]=(0,o.useState)(!0),[p,u]=(0,o.useState)(null),[g,m]=(0,o.useState)(!1);if((0,o.useEffect)(()=>{m(!0)},[]),(0,o.useEffect)(()=>{g&&(async()=>{try{console.log("JobList: Starting to fetch jobs..."),h(!0),u(null),console.log("JobList: Waiting for authentication to be ready..."),await new Promise(e=>setTimeout(e,200));let e=sessionStorage.getItem("token"),t=sessionStorage.getItem("user");if(console.log("JobList: Auth check - Token exists:",!!e,"User exists:",!!t),console.log("JobList: SessionStorage length:",sessionStorage.length),e)console.log("JobList: Token preview:",e.substring(0,20)+"..."),console.log("JobList: Token length:",e.length);else{console.log("JobList: No token found in sessionStorage"),console.log("JobList: All sessionStorage keys:",Object.keys(sessionStorage)),await new Promise(e=>setTimeout(e,500));let e=sessionStorage.getItem("token");if(console.log("JobList: Retry token check:",!!e),!e){console.log("JobList: Still no token after retry, user might not be logged in"),u("Please log in to view jobs"),h(!1);return}}console.log("JobList: Calling getJobs()...");let s=await (0,l.Ly)();console.log("JobList: Jobs fetched successfully:",s),console.log("JobList: Number of jobs:",s.length),c(s)}catch(s){console.error("JobList: Error fetching jobs:",s),console.error("JobList: Error details:",{message:s instanceof Error?s.message:"Unknown error",stack:s instanceof Error?s.stack:void 0});let e=s instanceof Error?s.message:"Failed to fetch jobs";u(e),null==t||t(e)}finally{h(!1)}})()},[g,t]),!g)return(0,r.jsxs)("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"24px"},children:[(0,r.jsx)("h3",{style:{fontSize:"18px",fontWeight:"500",margin:"0 0 16px 0"},children:"Work History"}),(0,r.jsx)("p",{style:{color:"#6b7280",margin:"0"},children:"Loading..."})]});let f=e=>{let t={Pending:{bg:"#ecfdf5",text:"#065f46",border:"#d1fae5"},InProgress:{bg:"#f0fdf4",text:"#14532d",border:"#bbf7d0"},UnderReview:{bg:"#ecfdf5",text:"#047857",border:"#a7f3d0"},Completed:{bg:"#d1fae5",text:"#064e3b",border:"#6ee7b7"},Cancelled:{bg:"#f9fafb",text:"#6b7280",border:"#e5e7eb"},OnHold:{bg:"#f3f4f6",text:"#4b5563",border:"#d1d5db"}},s=t[e]||t.OnHold;return(0,r.jsx)("span",{style:{display:"inline-flex",alignItems:"center",padding:"4px 12px",borderRadius:"9999px",fontSize:"12px",fontWeight:"500",backgroundColor:s.bg,color:s.text,border:"1px solid ".concat(s.border)},children:a(e)})},j=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});if(x)return(0,r.jsx)("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"24px"},children:(0,r.jsx)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"128px"},children:(0,r.jsx)("div",{style:{color:"#6b7280"},children:"Loading jobs..."})})});if(p)return(0,r.jsx)("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"24px"},children:(0,r.jsx)("div",{style:{borderRadius:"6px",backgroundColor:"#fef2f2",padding:"16px"},children:(0,r.jsxs)("div",{style:{display:"flex"},children:[(0,r.jsx)("div",{style:{flexShrink:0},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"#f87171"},viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{style:{marginLeft:"12px"},children:[(0,r.jsx)("h3",{style:{fontSize:"14px",fontWeight:"500",color:"#991b1b",margin:"0 0 8px 0"},children:"Error Loading Jobs"}),(0,r.jsx)("div",{style:{fontSize:"14px",color:"#7f1d1d",margin:"0"},children:p})]})]})})});if(0===d.length)return(0,r.jsx)("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px"},children:(0,r.jsxs)("div",{style:{padding:"24px"},children:[(0,r.jsx)("h3",{style:{fontSize:"18px",fontWeight:"500",color:"#111827",margin:"0 0 24px 0"},children:"Work History"}),(0,r.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"24px"},children:[(0,r.jsx)("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Total Jobs"}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:"0"})]})]})}),(0,r.jsx)("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Completed"}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:"0"})]})]})}),(0,r.jsx)("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"In Progress"}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:"0"})]})]})}),(0,r.jsx)("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Pending"}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:"0"})]})]})})]}),(0,r.jsxs)("div",{style:{textAlign:"center",padding:"32px 0"},children:[(0,r.jsx)("svg",{style:{margin:"0 auto",height:"48px",width:"48px",color:"#9ca3af"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,r.jsx)("h3",{style:{marginTop:"8px",fontSize:"14px",fontWeight:"500",color:"#111827",margin:"8px 0"},children:"No jobs found"}),(0,r.jsx)("p",{style:{marginTop:"4px",fontSize:"14px",color:"#6b7280",margin:"4px 0 24px 0"},children:s&&s.hoursAvailable<=0?"Purchase hours to start submitting jobs.":"Get started by submitting your first job."}),s&&s.hoursAvailable>0&&(0,r.jsx)("div",{children:(0,r.jsx)("a",{href:"/submit-job",style:{display:"inline-flex",alignItems:"center",padding:"8px 16px",border:"none",boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",fontSize:"14px",fontWeight:"500",borderRadius:"6px",color:"white",backgroundColor:"#059669",textDecoration:"none"},children:"Submit Job"})})]})]})});let b=d.length,v=d.filter(e=>"Completed"===e.status).length,y=d.filter(e=>"Pending"===e.status).length,w=d.filter(e=>"InProgress"===e.status).length;return(0,r.jsx)("div",{style:{backgroundColor:"white",boxShadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1)",borderRadius:"8px"},children:(0,r.jsxs)("div",{style:{padding:"24px"},children:[(0,r.jsx)("h3",{style:{fontSize:"18px",fontWeight:"500",color:"#111827",margin:"0 0 24px 0"},children:"Work History"}),(0,r.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"24px"},children:[(0,r.jsx)("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Total Jobs"}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:b})]})]})}),(0,r.jsx)("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Completed"}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:v})]})]})}),(0,r.jsx)("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"In Progress"}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:w})]})]})}),(0,r.jsx)("div",{style:{backgroundColor:"#ecfdf5",padding:"16px",borderRadius:"12px",border:"1px solid #d1fae5"},children:(0,r.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,r.jsx)("div",{style:{width:"40px",height:"40px",backgroundColor:"#059669",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",marginRight:"12px"},children:(0,r.jsx)("svg",{style:{height:"20px",width:"20px",color:"white"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#065f46"},children:"Pending"}),(0,r.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#064e3b"},children:y})]})]})})]}),(0,r.jsx)("div",{style:{overflow:"hidden"},children:(0,r.jsxs)("table",{style:{minWidth:"100%",borderCollapse:"collapse"},children:[(0,r.jsx)("thead",{style:{backgroundColor:"#f9fafb"},children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{style:{padding:"12px 24px",textAlign:"left",fontSize:"12px",fontWeight:"500",color:"#6b7280",textTransform:"uppercase",letterSpacing:"0.05em"},children:"Job"}),(0,r.jsx)("th",{style:{padding:"12px 24px",textAlign:"left",fontSize:"12px",fontWeight:"500",color:"#6b7280",textTransform:"uppercase",letterSpacing:"0.05em"},children:"Type"}),(0,r.jsx)("th",{style:{padding:"12px 24px",textAlign:"left",fontSize:"12px",fontWeight:"500",color:"#6b7280",textTransform:"uppercase",letterSpacing:"0.05em"},children:"Status"}),(0,r.jsx)("th",{style:{padding:"12px 24px",textAlign:"left",fontSize:"12px",fontWeight:"500",color:"#6b7280",textTransform:"uppercase",letterSpacing:"0.05em"},children:"Created"})]})}),(0,r.jsx)("tbody",{style:{backgroundColor:"white"},children:d.map((e,t)=>(0,r.jsxs)("tr",{style:{borderTop:t>0?"1px solid #e5e7eb":"none"},children:[(0,r.jsx)("td",{style:{padding:"16px 24px",whiteSpace:"nowrap"},children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontSize:"14px",fontWeight:"500",color:"#059669",cursor:"pointer",textDecoration:"underline"},onClick:()=>n.push("/jobs/".concat(e.id)),onMouseEnter:e=>{e.currentTarget.style.color="#047857"},onMouseLeave:e=>{e.currentTarget.style.color="#059669"},children:e.title}),(0,r.jsx)("div",{style:{fontSize:"14px",color:"#6b7280",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"300px"},children:e.description})]})}),(0,r.jsxs)("td",{style:{padding:"16px 24px",whiteSpace:"nowrap"},children:[(0,r.jsx)("div",{style:{fontSize:"14px",color:"#111827"},children:a(e.jobType)}),(0,r.jsx)("div",{style:{fontSize:"14px",color:"#6b7280"},children:a(e.category)})]}),(0,r.jsx)("td",{style:{padding:"16px 24px",whiteSpace:"nowrap"},children:f(e.status)}),(0,r.jsx)("td",{style:{padding:"16px 24px",whiteSpace:"nowrap",fontSize:"14px",color:"#6b7280"},children:j(e.createdAt)})]},e.id))})]})})]})})}function c(){let e=(0,i.useRouter)(),[t,s]=(0,o.useState)(!0),[l,a]=(0,o.useState)(null),[c,x]=(0,o.useState)(null),[h,p]=(0,o.useState)({hoursBought:0,hoursUtilized:0,hoursAvailable:0}),[u,g]=(0,o.useState)(null),[m,f]=(0,o.useState)(!0);(0,o.useEffect)(()=>{(async()=>{try{let t=sessionStorage.getItem("token"),s=sessionStorage.getItem("user");if(console.log("Dashboard: Checking authentication...",{hasToken:!!t,hasUser:!!s}),!t||!s){console.log("Dashboard: No authentication found, redirecting to login"),e.push("/login");return}let r=JSON.parse(s);if(console.log("Dashboard: User data:",r),"Admin"===r.role){e.push("/admin");return}if("Supervisor"===r.role){e.push("/supervisor");return}if("Staff"===r.role){e.push("/staff");return}x(r);let o=await (0,n.N)();console.log("Dashboard: Statistics received:",o),p(o);try{console.log("Dashboard: Fetching account statement...");let e=await (0,n.j)();console.log("Dashboard: Account statement received:",e),g(e)}catch(e){console.error("Dashboard: Error fetching account statement:",e)}finally{f(!1)}}catch(s){console.error("Dashboard: Error fetching statistics:",s);let t=s instanceof Error?s.message:"Failed to fetch statistics";a(t),(t.includes("Authentication required")||t.includes("401"))&&(console.log("Dashboard: Authentication error, redirecting to login"),e.push("/login"))}finally{s(!1)}})()},[e]);let j=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e),b=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),v=e=>e.toFixed(2);return t?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex justify-between h-16",children:(0,r.jsx)("div",{className:"flex",children:(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,r.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),(0,r.jsx)("main",{className:"pt-24 py-10",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"text-gray-500",children:"Loading statistics..."})})})})]}):l?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"flex justify-between h-16",children:(0,r.jsx)("div",{className:"flex",children:(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,r.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),(0,r.jsx)("main",{className:"pt-24 py-10",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"rounded-md bg-red-50 p-4 mb-4",children:(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error Loading Dashboard"}),(0,r.jsx)("div",{className:"mt-2 text-sm text-red-700",children:l}),(0,r.jsx)("div",{className:"mt-3",children:(0,r.jsx)("button",{onClick:()=>{window.location.reload()},className:"bg-red-100 px-2 py-1 text-sm font-medium text-red-800 rounded-md hover:bg-red-200",children:"Retry"})})]})]})})})})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,r.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,r.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})}),(0,r.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[(0,r.jsx)("a",{href:"/dashboard",className:"border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),(0,r.jsx)("a",{href:"/submit-job",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Submit Job"}),(0,r.jsx)("a",{href:"/buy-hours",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Buy Hours"})]})]}),(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:(0,r.jsx)("span",{children:"Sign Out"})})})]})})}),(0,r.jsx)("main",{className:"pt-24 py-10",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 mb-8",children:"Account Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-3 mb-8",children:[(0,r.jsx)("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Hours Bought"}),(0,r.jsx)("div",{className:"text-3xl font-bold text-emerald-900",children:h.hoursBought})]})]})}),(0,r.jsx)("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Hours Used"}),(0,r.jsx)("div",{className:"text-3xl font-bold text-emerald-900",children:h.hoursUtilized})]})]})}),(0,r.jsx)("div",{className:"bg-emerald-50 p-6 rounded-xl border border-emerald-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Available"}),(0,r.jsx)("div",{className:"text-3xl font-bold text-emerald-900",children:h.hoursAvailable})]})]})})]}),h.hoursAvailable<=0&&(0,r.jsx)("div",{className:"mt-8 bg-emerald-50 border border-emerald-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"h-8 w-8 text-emerald-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-emerald-800",children:"No Hours Available"}),(0,r.jsx)("p",{className:"text-sm text-emerald-700",children:"You need to purchase hours to submit jobs and access our services."})]})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsxs)("a",{href:"/buy-hours",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 shadow-sm",children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Buy Hours Now"]})})]})}),(0,r.jsx)("div",{style:{marginTop:"32px"},children:(0,r.jsx)(d,{hoursStatistics:h,onError:t=>{console.error("JobList error:",t),(t.includes("Authentication required")||t.includes("401"))&&e.push("/login")}})}),(0,r.jsxs)("div",{className:"mt-8 bg-white shadow rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium text-gray-900 mb-6",children:"Statement of Account"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsx)("div",{className:"bg-emerald-50 p-4 rounded-xl border border-emerald-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Total Hours Bought"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-emerald-900",children:u?v(u.totalHoursBought):v(h.hoursBought)})]})]})}),(0,r.jsx)("div",{className:"bg-emerald-50 p-4 rounded-xl border border-emerald-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Total Hours Spent"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-emerald-900",children:u?v(u.totalHoursSpent):v(h.hoursUtilized)})]})]})}),(0,r.jsx)("div",{className:"bg-emerald-50 p-4 rounded-xl border border-emerald-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Hours Balance"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-emerald-900",children:u?v(u.currentHoursBalance):v(h.hoursAvailable)})]})]})}),(0,r.jsx)("div",{className:"bg-emerald-50 p-4 rounded-xl border border-emerald-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-emerald-500 rounded-full flex items-center justify-center mr-3",children:(0,r.jsx)("svg",{className:"h-5 w-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-emerald-700",children:"Amount Balance"}),(0,r.jsx)("div",{className:"text-2xl font-bold text-emerald-900",children:u?j(u.currentAmountBalance):"$0.00"})]})]})})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Reference"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Transaction"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Hours Bought"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Hours Spent"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Adjustment"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Balance Hours"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Balance Amount"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:m?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:10,className:"px-6 py-8 text-center text-gray-500",children:"Loading transactions..."})}):u&&u.entries.length>0?u.entries.map((e,t)=>(0,r.jsxs)("tr",{className:t%2==0?"bg-white":"bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:b(e.date)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("Purchase"===e.type?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.type})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.reference}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-xs truncate",children:e.transaction}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:e.hoursBought>0?v(e.hoursBought):"-"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:e.hoursSpent>0?v(e.hoursSpent):"-"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:0!==e.adjustment?v(e.adjustment):"-"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900",children:v(e.balanceHours)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900",children:0!==e.amount?j(e.amount):"-"}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-900",children:j(e.balanceAmount)})]},t)):(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:10,className:"px-6 py-8 text-center text-gray-500",children:"No transactions found. Purchase hours or submit jobs to see your statement."})})})]})})]})]})})]})}},7567:function(e,t,s){"use strict";s.d(t,{N:function(){return i},j:function(){return n}});var r=s(3464);let o="http://localhost:5000",i=async()=>{let e=sessionStorage.getItem("token"),t=sessionStorage.getItem("user");if(!e||!t)throw Error("Authentication required");let s=JSON.parse(t);console.log("Fetching statistics for clientId:",s.clientId);try{let t={Authorization:"Bearer ".concat(e),"Content-Type":"application/json"};console.log("Request headers:",t);let i=await r.Z.get("".concat(o,"/api/statistics/hours/").concat(s.clientId),{headers:t});return console.log("API Response:",i.data),i.data}catch(e){if(r.Z.isAxiosError(e)){var i,n,l,a,d;console.error("API Error:",{status:null===(i=e.response)||void 0===i?void 0:i.status,statusText:null===(n=e.response)||void 0===n?void 0:n.statusText,data:null===(l=e.response)||void 0===l?void 0:l.data,clientId:s.clientId,url:"".concat(o,"/api/statistics/hours/").concat(s.clientId)});let t=null===(a=e.response)||void 0===a?void 0:a.status,r=null===(d=e.response)||void 0===d?void 0:d.data;if(401===t)throw Error("Authentication required - please log in again");if(404===t)throw Error("Client data not found - please contact support");if(500===t)throw Error("Server error - please try again later");{let t="string"==typeof r?r:(null==r?void 0:r.message)||e.message||"Unknown error occurred";throw Error("Failed to fetch statistics: ".concat(t))}}if(e instanceof Error)throw Error("Failed to fetch statistics: ".concat(e.message));throw Error("Failed to fetch statistics: An unknown error occurred")}},n=async()=>{let e=sessionStorage.getItem("token"),t=sessionStorage.getItem("user");if(!e||!t)throw Error("Authentication required");let s=JSON.parse(t);console.log("Fetching account statement for clientId:",s.clientId);try{let t={Authorization:"Bearer ".concat(e),"Content-Type":"application/json"};console.log("Request headers:",t);let i=await r.Z.get("".concat(o,"/api/statistics/statement/").concat(s.clientId),{headers:t});return console.log("Account Statement API Response:",i.data),i.data}catch(e){if(r.Z.isAxiosError(e)){var i,n,l,a,d;console.error("Account Statement API Error:",{status:null===(i=e.response)||void 0===i?void 0:i.status,statusText:null===(n=e.response)||void 0===n?void 0:n.statusText,data:null===(l=e.response)||void 0===l?void 0:l.data,clientId:s.clientId,url:"".concat(o,"/api/statistics/statement/").concat(s.clientId)});let t=null===(a=e.response)||void 0===a?void 0:a.status,r=null===(d=e.response)||void 0===d?void 0:d.data;if(401===t)throw Error("Authentication required - please log in again");if(404===t)throw Error("Account statement not found - please contact support");if(500===t)throw Error("Server error - please try again later");{let t="string"==typeof r?r:(null==r?void 0:r.message)||e.message||"Unknown error occurred";throw Error("Failed to fetch account statement: ".concat(t))}}if(e instanceof Error)throw Error("Failed to fetch account statement: ".concat(e.message));throw Error("Failed to fetch account statement: An unknown error occurred")}}},9740:function(e,t,s){"use strict";s.d(t,{Jk:function(){return n},Ly:function(){return i}});var r=s(3464);let o="http://localhost:5000",i=async()=>{console.log("getJobs: Starting..."),await new Promise(e=>setTimeout(e,100));let e=sessionStorage.getItem("token"),t=sessionStorage.getItem("user");if(console.log("getJobs: Token exists:",!!e),console.log("getJobs: User exists:",!!t),console.log("getJobs: SessionStorage keys:",Object.keys(sessionStorage)),e?(console.log("getJobs: Token preview:",e.substring(0,20)+"..."),console.log("getJobs: Token length:",e.length)):(console.log("getJobs: Token is null/undefined/empty"),console.log("getJobs: All sessionStorage items:",{token:sessionStorage.getItem("token"),user:sessionStorage.getItem("user")})),!e)throw console.log("getJobs: No token found, throwing auth error"),Error("Authentication required");console.log("getJobs: Making API request to:","".concat(o,"/api/jobs"));try{let t=await r.Z.get("".concat(o,"/api/jobs"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});return console.log("getJobs: Response status:",t.status),console.log("getJobs: Response data type:",typeof t.data),console.log("getJobs: Response data length:",Array.isArray(t.data)?t.data.length:"Not an array"),console.log("getJobs: Response data:",t.data),t.data}catch(e){if(console.error("getJobs: API request failed:",e),r.Z.isAxiosError(e)){var s,i,n,l;if(console.error("getJobs: Axios error details:",{status:null===(s=e.response)||void 0===s?void 0:s.status,statusText:null===(i=e.response)||void 0===i?void 0:i.statusText,data:null===(n=e.response)||void 0===n?void 0:n.data}),(null===(l=e.response)||void 0===l?void 0:l.status)===401)throw Error("Authentication required")}throw e}},n=async e=>{console.log("getJobById: Starting for job ID:",e);let t=sessionStorage.getItem("token");if(!t)throw console.log("getJobById: No token found"),Error("Authentication required");try{console.log("getJobById: Making API request to:","".concat(o,"/api/jobs/").concat(e)),console.log("getJobById: Using token:",t.substring(0,20)+"...");let s=await r.Z.get("".concat(o,"/api/jobs/").concat(e),{headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"}});return console.log("getJobById: Response status:",s.status),console.log("getJobById: Response data:",s.data),s.data}catch(e){if(console.error("getJobById: API request failed:",e),r.Z.isAxiosError(e)){var s,i,n,l,a,d;if(console.error("getJobById: Axios error details:",{status:null===(s=e.response)||void 0===s?void 0:s.status,statusText:null===(i=e.response)||void 0===i?void 0:i.statusText,data:null===(n=e.response)||void 0===n?void 0:n.data}),(null===(l=e.response)||void 0===l?void 0:l.status)===401)throw Error("Authentication required");if((null===(a=e.response)||void 0===a?void 0:a.status)===404)throw Error("Job not found");if((null===(d=e.response)||void 0===d?void 0:d.status)===403)throw Error("Access denied")}throw e}}}},function(e){e.O(0,[301,971,117,744],function(){return e(e.s=5858)}),_N_E=e.O()}]);