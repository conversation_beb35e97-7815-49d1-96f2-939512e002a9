(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[712],{4908:function(e,s,t){Promise.resolve().then(t.bind(t,6645))},6645:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return n}});var a=t(7437),r=t(2265),l=t(9376),o=t(9740);function n(){let e=(0,l.useRouter)(),s=(0,l.useParams)(),[t,n]=(0,r.useState)(null),[d,i]=(0,r.useState)(!0),[c,m]=(0,r.useState)(null),x=s.id?parseInt(s.id):null;(0,r.useEffect)(()=>{if(!x){m("Invalid job ID"),i(!1);return}(async()=>{try{console.log("JobDetailsPage: Fetching job with ID:",x);let e=await (0,o.Jk)(x);console.log("JobDetailsPage: Job fetched successfully:",e),n(e)}catch(t){console.error("JobDetailsPage: Error fetching job:",t);let s=t instanceof Error?t.message:"Failed to fetch job details";m(s),(s.includes("Authentication required")||s.includes("401"))&&e.push("/login")}finally{i(!1)}})()},[x,e]);let h=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),g=e=>{let s={Pending:{bg:"#ecfdf5",text:"#065f46",border:"#d1fae5"},InProgress:{bg:"#f0fdf4",text:"#14532d",border:"#bbf7d0"},UnderReview:{bg:"#ecfdf5",text:"#047857",border:"#a7f3d0"},Completed:{bg:"#d1fae5",text:"#064e3b",border:"#6ee7b7"},Cancelled:{bg:"#f9fafb",text:"#6b7280",border:"#e5e7eb"},OnHold:{bg:"#f3f4f6",text:"#4b5563",border:"#d1d5db"}}[e]||{bg:"#f3f4f6",text:"#4b5563",border:"#d1d5db"};return(0,a.jsx)("span",{style:{backgroundColor:s.bg,color:s.text,border:"1px solid ".concat(s.border),padding:"4px 12px",borderRadius:"9999px",fontSize:"12px",fontWeight:"500"},children:e})};return d?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex justify-between h-16",children:(0,a.jsx)("div",{className:"flex",children:(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),(0,a.jsx)("main",{className:"pt-24 py-10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading job details..."})]})})})})]}):c?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex justify-between h-16",children:(0,a.jsx)("div",{className:"flex",children:(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),(0,a.jsx)("main",{className:"pt-24 py-10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,a.jsx)("p",{children:c})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("button",{onClick:()=>e.push("/dashboard"),className:"bg-emerald-100 hover:bg-emerald-200 text-emerald-800 px-4 py-2 rounded-md text-sm font-medium",children:"Back to Dashboard"})})]})]})})})})]}):t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})}),(0,a.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[(0,a.jsx)("a",{href:"/dashboard",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),(0,a.jsx)("a",{href:"/submit-job",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Submit Job"}),(0,a.jsx)("a",{href:"/buy-hours",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Buy Hours"})]})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:(0,a.jsx)("span",{children:"Sign Out"})})})]})})}),(0,a.jsx)("main",{className:"pt-24 py-10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("button",{onClick:()=>e.push("/dashboard"),className:"mb-4 text-emerald-600 hover:text-emerald-800 text-sm font-medium flex items-center",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Dashboard"]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:t.title}),g(t.status)]})]}),(0,a.jsxs)("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:[(0,a.jsxs)("div",{className:"px-4 py-5 sm:px-6",children:[(0,a.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900",children:"Job Information"}),(0,a.jsx)("p",{className:"mt-1 max-w-2xl text-sm text-gray-500",children:"Complete details about this job."})]}),(0,a.jsx)("div",{className:"border-t border-gray-200",children:(0,a.jsxs)("dl",{children:[(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Job ID"}),(0,a.jsxs)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:["#",t.id]})]}),(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Description"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t.description})]}),(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Job Type"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t.jobType})]}),(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Category"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t.category})]}),(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Output Format"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:t.outputFormat})]}),(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Status"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:g(t.status)})]}),(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Created"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:h(t.createdAt)})]}),t.updatedAt&&(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:h(t.updatedAt)})]}),t.attachmentUrl&&(0,a.jsxs)("div",{className:"bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Attachment"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:(0,a.jsx)("a",{href:t.attachmentUrl,target:"_blank",rel:"noopener noreferrer",className:"text-emerald-600 hover:text-emerald-800 underline",children:"View Attachment"})})]}),t.referenceUrl&&(0,a.jsxs)("div",{className:"bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6",children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Reference URL"}),(0,a.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2",children:(0,a.jsx)("a",{href:t.referenceUrl,target:"_blank",rel:"noopener noreferrer",className:"text-emerald-600 hover:text-emerald-800 underline",children:"View Reference"})})]})]})})]})]})})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,a.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"flex justify-between h-16",children:(0,a.jsx)("div",{className:"flex",children:(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})})})})})}),(0,a.jsx)("main",{className:"pt-24 py-10",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"Job not found"}),(0,a.jsx)("button",{onClick:()=>e.push("/dashboard"),className:"mt-4 bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Back to Dashboard"})]})})})]})}},9740:function(e,s,t){"use strict";t.d(s,{Jk:function(){return o},Ly:function(){return l}});var a=t(3464);let r="http://localhost:5000",l=async()=>{console.log("getJobs: Starting..."),await new Promise(e=>setTimeout(e,100));let e=sessionStorage.getItem("token"),s=sessionStorage.getItem("user");if(console.log("getJobs: Token exists:",!!e),console.log("getJobs: User exists:",!!s),console.log("getJobs: SessionStorage keys:",Object.keys(sessionStorage)),e?(console.log("getJobs: Token preview:",e.substring(0,20)+"..."),console.log("getJobs: Token length:",e.length)):(console.log("getJobs: Token is null/undefined/empty"),console.log("getJobs: All sessionStorage items:",{token:sessionStorage.getItem("token"),user:sessionStorage.getItem("user")})),!e)throw console.log("getJobs: No token found, throwing auth error"),Error("Authentication required");console.log("getJobs: Making API request to:","".concat(r,"/api/jobs"));try{let s=await a.Z.get("".concat(r,"/api/jobs"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});return console.log("getJobs: Response status:",s.status),console.log("getJobs: Response data type:",typeof s.data),console.log("getJobs: Response data length:",Array.isArray(s.data)?s.data.length:"Not an array"),console.log("getJobs: Response data:",s.data),s.data}catch(e){if(console.error("getJobs: API request failed:",e),a.Z.isAxiosError(e)){var t,l,o,n;if(console.error("getJobs: Axios error details:",{status:null===(t=e.response)||void 0===t?void 0:t.status,statusText:null===(l=e.response)||void 0===l?void 0:l.statusText,data:null===(o=e.response)||void 0===o?void 0:o.data}),(null===(n=e.response)||void 0===n?void 0:n.status)===401)throw Error("Authentication required")}throw e}},o=async e=>{console.log("getJobById: Starting for job ID:",e);let s=sessionStorage.getItem("token");if(!s)throw console.log("getJobById: No token found"),Error("Authentication required");try{console.log("getJobById: Making API request to:","".concat(r,"/api/jobs/").concat(e)),console.log("getJobById: Using token:",s.substring(0,20)+"...");let t=await a.Z.get("".concat(r,"/api/jobs/").concat(e),{headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"}});return console.log("getJobById: Response status:",t.status),console.log("getJobById: Response data:",t.data),t.data}catch(e){if(console.error("getJobById: API request failed:",e),a.Z.isAxiosError(e)){var t,l,o,n,d,i;if(console.error("getJobById: Axios error details:",{status:null===(t=e.response)||void 0===t?void 0:t.status,statusText:null===(l=e.response)||void 0===l?void 0:l.statusText,data:null===(o=e.response)||void 0===o?void 0:o.data}),(null===(n=e.response)||void 0===n?void 0:n.status)===401)throw Error("Authentication required");if((null===(d=e.response)||void 0===d?void 0:d.status)===404)throw Error("Job not found");if((null===(i=e.response)||void 0===i?void 0:i.status)===403)throw Error("Access denied")}throw e}}}},function(e){e.O(0,[301,971,117,744],function(){return e(e.s=4908)}),_N_E=e.O()}]);