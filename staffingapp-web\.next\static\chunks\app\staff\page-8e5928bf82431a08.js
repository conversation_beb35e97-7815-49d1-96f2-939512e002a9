(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[533],{7321:function(e,s,t){Promise.resolve().then(t.bind(t,8574))},9376:function(e,s,t){"use strict";var a=t(5475);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},8574:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var a=t(7437),r=t(2265),l=t(9376);let n=e=>e.replace(/([A-Z])/g," $1").trim();function i(){let e=(0,l.useRouter)(),[s,t]=(0,r.useState)(!0),[i,d]=(0,r.useState)(null),[c,o]=(0,r.useState)([]),[m,x]=(0,r.useState)("assigned");(0,r.useEffect)(()=>{(()=>{let s=sessionStorage.getItem("token"),t=sessionStorage.getItem("user");if(!s||!t){e.push("/login");return}let a=JSON.parse(t);if("Staff"!==a.role){e.push("/dashboard");return}d(a),h()})()},[e]);let h=async()=>{try{let e=sessionStorage.getItem("token"),s=await fetch("http://localhost:5000/api/jobs/assigned",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(s.ok){let e=await s.json();o(e)}}catch(e){console.error("Error fetching assigned jobs:",e)}finally{t(!1)}},g=e=>c.filter(s=>s.status===e),p=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),u=async e=>{try{let s=sessionStorage.getItem("token");(await fetch("http://localhost:5000/api/jobs/".concat(e,"/start"),{method:"PUT",headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"}})).ok&&h()}catch(e){console.error("Error starting job:",e)}},j=async e=>{try{let s=sessionStorage.getItem("token");(await fetch("http://localhost:5000/api/jobs/".concat(e,"/complete"),{method:"PUT",headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"}})).ok&&h()}catch(e){console.error("Error completing job:",e)}};if(s)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading staff dashboard..."})]})});let N=g("Assigned"),b=g("InProgress"),f=g("Completed");return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Staff Hall - Staff Dashboard"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",null==i?void 0:i.firstName," ",null==i?void 0:i.lastName]}),(0,a.jsx)("button",{onClick:()=>{sessionStorage.removeItem("token"),sessionStorage.removeItem("user"),e.push("/login")},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Logout"})]})]})})}),(0,a.jsx)("main",{className:"pt-24 py-10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:N.length})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"New Assignments"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[N.length," jobs"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:b.length})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"In Progress"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[b.length," jobs"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:f.length})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Completed"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[f.length," jobs"]})]})})]})})})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"assigned",name:"New Assignments",count:N.length},{id:"progress",name:"In Progress",count:b.length},{id:"completed",name:"Completed",count:f.length}].map(e=>(0,a.jsxs)("button",{onClick:()=>x(e.id),className:"".concat(m===e.id?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"," whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"),children:[e.name,(0,a.jsx)("span",{className:"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs",children:e.count})]},e.id))})}),(0,a.jsxs)("div",{className:"p-6",children:["assigned"===m&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"New Job Assignments"}),0===N.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No new job assignments"}):(0,a.jsx)("div",{className:"space-y-4",children:N.map(s=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",n(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",n(s.category)]}),(0,a.jsxs)("span",{children:["Assigned: ",s.assignedAt?p(s.assignedAt):"N/A"]})]}),s.supervisorNotes&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-blue-50 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"Supervisor Notes:"})," ",s.supervisorNotes]})})]}),(0,a.jsxs)("div",{className:"ml-4 flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e.push("/jobs/".concat(s.id)),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),(0,a.jsx)("button",{onClick:()=>u(s.id),className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded text-sm",children:"Start Working"})]})]})},s.id))})]}),"progress"===m&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs In Progress"}),0===b.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No jobs in progress"}):(0,a.jsx)("div",{className:"space-y-4",children:b.map(s=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",n(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",n(s.category)]}),(0,a.jsxs)("span",{children:["Started: ",s.assignedAt?p(s.assignedAt):"N/A"]})]})]}),(0,a.jsxs)("div",{className:"ml-4 flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e.push("/jobs/".concat(s.id)),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),(0,a.jsx)("button",{onClick:()=>j(s.id),className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm",children:"Mark Complete"})]})]})},s.id))})]}),"completed"===m&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Completed Jobs"}),0===f.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No completed jobs"}):(0,a.jsx)("div",{className:"space-y-4",children:f.map(s=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 bg-green-50",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",n(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",n(s.category)]}),(0,a.jsxs)("span",{children:["Completed: ",s.updatedAt?p(s.updatedAt):"N/A"]})]})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e.push("/jobs/".concat(s.id)),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),(0,a.jsx)("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800",children:"Completed"})]})]})},s.id))})]})]})]})]})})]})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=7321)}),_N_E=e.O()}]);