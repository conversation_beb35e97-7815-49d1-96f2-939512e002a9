(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[845],{7573:function(e,t,r){Promise.resolve().then(r.bind(r,9097))},9097:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return c}});var o=r(7437),a=r(2265),s=r(9376),n=r(1981),l=r(7567);let i=e=>e.replace(/([A-Z])/g," $1").trim();function c(){let e=(0,s.useRouter)(),[t,r]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null),[u,m]=(0,a.useState)(!0),[h,p]=(0,a.useState)({hoursBought:0,hoursUtilized:0,hoursAvailable:0}),[x,g]=(0,a.useState)({title:"",description:"",jobType:n.O4.DataEntry,category:n.WZ.DataProcessing,outputFormat:n.WJ.PDF,attachmentUrl:"",referenceUrl:"",clientId:0});(0,a.useEffect)(()=>{(async()=>{try{m(!0);let e=await (0,l.N)();p(e)}catch(t){console.error("Error fetching hours statistics:",t),t instanceof Error&&(t.message.includes("Authentication required")||t.message.includes("401"))&&e.push("/login")}finally{m(!1)}})()},[e]);let f=async t=>{if(t.preventDefault(),r(!0),d(null),h.hoursAvailable<=0){d("You need to purchase hours before submitting a job. Please buy hours first."),r(!1);return}try{let t=sessionStorage.getItem("user"),r=sessionStorage.getItem("token");if(console.log("Debug - sessionStorage contents:",{hasUser:!!t,hasToken:!!r,userStr:t,tokenLength:(null==r?void 0:r.length)||0}),!t||!r)throw Error("Authentication required - Please login first");let o=JSON.parse(t),a=await fetch("http://localhost:5000/api/jobs",{method:"POST",headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"},body:JSON.stringify({...x,clientId:o.clientId})});if(!a.ok){let e=await a.text();throw Error("Failed to submit job: ".concat(e))}d(null);let s=document.createElement("div");s.className="fixed top-4 right-4 bg-emerald-50 border border-emerald-200 text-emerald-800 px-6 py-4 rounded-lg shadow-lg z-50 flex items-center",s.innerHTML='\n        <svg class="h-5 w-5 text-emerald-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">\n          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>\n        </svg>\n        <span class="font-medium">Job submitted successfully! Redirecting to dashboard...</span>\n      ',document.body.appendChild(s),setTimeout(()=>{document.body.removeChild(s),e.push("/dashboard")},2e3)}catch(e){d(e instanceof Error?e.message:"Failed to submit job")}finally{r(!1)}};return(0,o.jsxs)("div",{className:"min-h-screen bg-gray-100",children:[(0,o.jsx)("nav",{className:"fixed top-0 left-0 right-0 bg-white shadow-sm z-50",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"flex justify-between h-16",children:[(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,o.jsx)("span",{className:"text-xl font-bold text-emerald-600",children:"Staff Hall"})}),(0,o.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[(0,o.jsx)("a",{href:"/dashboard",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),(0,o.jsx)("a",{href:"/submit-job",className:"border-emerald-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Submit Job"}),(0,o.jsx)("a",{href:"/buy-hours",className:"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Buy Hours"})]})]}),(0,o.jsx)("div",{className:"flex items-center",children:(0,o.jsx)("button",{type:"button",className:"bg-white p-1 rounded-full text-gray-400 hover:text-gray-500",onClick:()=>{sessionStorage.clear(),document.cookie="token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;",window.location.href="/login"},children:(0,o.jsx)("span",{children:"Sign Out"})})})]})})}),(0,o.jsx)("main",{className:"pt-24 py-10",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Submit New Job"}),!u&&h.hoursAvailable<=0&&(0,o.jsx)("div",{className:"bg-emerald-50 border border-emerald-200 rounded-md p-4 mb-6",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("svg",{className:"h-5 w-5 text-emerald-500",viewBox:"0 0 20 20",fill:"currentColor",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})})}),(0,o.jsxs)("div",{className:"ml-3",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-emerald-800",children:"No Hours Available"}),(0,o.jsx)("div",{className:"mt-2 text-sm text-emerald-700",children:(0,o.jsx)("p",{children:"You need to purchase hours before submitting a job. Please buy hours to continue."})}),(0,o.jsx)("div",{className:"mt-4",children:(0,o.jsx)("a",{href:"/buy-hours",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500",children:"Buy Hours Now"})})]})]})}),c&&(0,o.jsx)("div",{className:"bg-red-50 p-4 rounded-md mb-6",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,o.jsxs)("div",{className:"ml-3",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Error Submitting Job"}),(0,o.jsx)("div",{className:"mt-2 text-sm text-red-700",children:c})]})]})}),(0,o.jsxs)("form",{onSubmit:f,className:"space-y-6 bg-white shadow px-6 py-8 rounded-lg",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700",children:"Job Title *"}),(0,o.jsx)("input",{type:"text",id:"title",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:x.title,onChange:e=>g({...x,title:e.target.value}),placeholder:"Enter job title"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700",children:"Description *"}),(0,o.jsx)("textarea",{id:"description",required:!0,rows:4,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:x.description,onChange:e=>g({...x,description:e.target.value}),placeholder:"Describe the job requirements and details"})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"jobType",className:"block text-sm font-medium text-gray-700",children:"Job Type *"}),(0,o.jsx)("select",{id:"jobType",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:x.jobType,onChange:e=>g({...x,jobType:e.target.value}),children:Object.values(n.O4).map(e=>(0,o.jsx)("option",{value:e,children:i(e)},e))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700",children:"Category *"}),(0,o.jsx)("select",{id:"category",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:x.category,onChange:e=>g({...x,category:e.target.value}),children:Object.values(n.WZ).map(e=>(0,o.jsx)("option",{value:e,children:i(e)},e))})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"outputFormat",className:"block text-sm font-medium text-gray-700",children:"Output Format *"}),(0,o.jsx)("select",{id:"outputFormat",required:!0,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:x.outputFormat,onChange:e=>g({...x,outputFormat:e.target.value}),children:Object.values(n.WJ).map(e=>(0,o.jsx)("option",{value:e,children:i(e)},e))})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"attachmentUrl",className:"block text-sm font-medium text-gray-700",children:"Attachment URL"}),(0,o.jsx)("input",{type:"url",id:"attachmentUrl",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:x.attachmentUrl,onChange:e=>g({...x,attachmentUrl:e.target.value}),placeholder:"https://example.com/attachment.pdf"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"referenceUrl",className:"block text-sm font-medium text-gray-700",children:"Reference URL"}),(0,o.jsx)("input",{type:"url",id:"referenceUrl",className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",value:x.referenceUrl,onChange:e=>g({...x,referenceUrl:e.target.value}),placeholder:"https://example.com/reference"})]})]}),(0,o.jsxs)("div",{className:"flex gap-4",children:[(0,o.jsx)("button",{type:"button",onClick:()=>e.push("/dashboard"),className:"flex-1 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500",children:"Cancel"}),(0,o.jsx)("button",{type:"submit",disabled:t||u||h.hoursAvailable<=0,className:"flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed",children:t?"Submitting...":u?"Loading...":h.hoursAvailable<=0?"Buy Hours to Submit Job":"Submit Job"})]})]})]})})})]})}},7567:function(e,t,r){"use strict";r.d(t,{N:function(){return s},j:function(){return n}});var o=r(3464);let a="http://localhost:5000",s=async()=>{let e=sessionStorage.getItem("token"),t=sessionStorage.getItem("user");if(!e||!t)throw Error("Authentication required");let r=JSON.parse(t);console.log("Fetching statistics for clientId:",r.clientId);try{let t={Authorization:"Bearer ".concat(e),"Content-Type":"application/json"};console.log("Request headers:",t);let s=await o.Z.get("".concat(a,"/api/statistics/hours/").concat(r.clientId),{headers:t});return console.log("API Response:",s.data),s.data}catch(e){if(o.Z.isAxiosError(e)){var s,n,l,i,c;console.error("API Error:",{status:null===(s=e.response)||void 0===s?void 0:s.status,statusText:null===(n=e.response)||void 0===n?void 0:n.statusText,data:null===(l=e.response)||void 0===l?void 0:l.data,clientId:r.clientId,url:"".concat(a,"/api/statistics/hours/").concat(r.clientId)});let t=null===(i=e.response)||void 0===i?void 0:i.status,o=null===(c=e.response)||void 0===c?void 0:c.data;if(401===t)throw Error("Authentication required - please log in again");if(404===t)throw Error("Client data not found - please contact support");if(500===t)throw Error("Server error - please try again later");{let t="string"==typeof o?o:(null==o?void 0:o.message)||e.message||"Unknown error occurred";throw Error("Failed to fetch statistics: ".concat(t))}}if(e instanceof Error)throw Error("Failed to fetch statistics: ".concat(e.message));throw Error("Failed to fetch statistics: An unknown error occurred")}},n=async()=>{let e=sessionStorage.getItem("token"),t=sessionStorage.getItem("user");if(!e||!t)throw Error("Authentication required");let r=JSON.parse(t);console.log("Fetching account statement for clientId:",r.clientId);try{let t={Authorization:"Bearer ".concat(e),"Content-Type":"application/json"};console.log("Request headers:",t);let s=await o.Z.get("".concat(a,"/api/statistics/statement/").concat(r.clientId),{headers:t});return console.log("Account Statement API Response:",s.data),s.data}catch(e){if(o.Z.isAxiosError(e)){var s,n,l,i,c;console.error("Account Statement API Error:",{status:null===(s=e.response)||void 0===s?void 0:s.status,statusText:null===(n=e.response)||void 0===n?void 0:n.statusText,data:null===(l=e.response)||void 0===l?void 0:l.data,clientId:r.clientId,url:"".concat(a,"/api/statistics/statement/").concat(r.clientId)});let t=null===(i=e.response)||void 0===i?void 0:i.status,o=null===(c=e.response)||void 0===c?void 0:c.data;if(401===t)throw Error("Authentication required - please log in again");if(404===t)throw Error("Account statement not found - please contact support");if(500===t)throw Error("Server error - please try again later");{let t="string"==typeof o?o:(null==o?void 0:o.message)||e.message||"Unknown error occurred";throw Error("Failed to fetch account statement: ".concat(t))}}if(e instanceof Error)throw Error("Failed to fetch account statement: ".concat(e.message));throw Error("Failed to fetch account statement: An unknown error occurred")}}},1981:function(e,t,r){"use strict";var o,a,s,n,l,i,c,d,u,m;r.d(t,{O4:function(){return o},WJ:function(){return s},WZ:function(){return a}}),(i=o||(o={})).DataEntry="DataEntry",i.Accounting="Accounting",i.HR="HR",i.ITSupport="ITSupport",i.Marketing="Marketing",i.Legal="Legal",i.CustomerService="CustomerService",i.Other="Other",(c=a||(a={})).DataProcessing="DataProcessing",c.DataCleaning="DataCleaning",c.DocumentationEntry="DocumentationEntry",c.Bookkeeping="Bookkeeping",c.FinancialReporting="FinancialReporting",c.Taxation="Taxation",c.Payroll="Payroll",c.Recruitment="Recruitment",c.EmployeeRelations="EmployeeRelations",c.Training="Training",c.CompensationBenefits="CompensationBenefits",c.TechnicalSupport="TechnicalSupport",c.NetworkSupport="NetworkSupport",c.SoftwareSupport="SoftwareSupport",c.HardwareSupport="HardwareSupport",c.DigitalMarketing="DigitalMarketing",c.ContentCreation="ContentCreation",c.SocialMedia="SocialMedia",c.MarketResearch="MarketResearch",c.ContractReview="ContractReview",c.Compliance="Compliance",c.LegalResearch="LegalResearch",c.Documentation="Documentation",c.CallCenter="CallCenter",c.EmailSupport="EmailSupport",c.ChatSupport="ChatSupport",c.CustomerFeedback="CustomerFeedback",c.Other="Other",(d=s||(s={})).PDF="PDF",d.Word="Word",d.Excel="Excel",d.PlainText="PlainText",d.JSON="JSON",d.XML="XML",d.Database="Database",d.Other="Other",(u=n||(n={})).Pending="Pending",u.UnderReview="UnderReview",u.Assigned="Assigned",u.InProgress="InProgress",u.Completed="Completed",u.Delivered="Delivered",u.Cancelled="Cancelled",u.OnHold="OnHold",u.Rejected="Rejected",(m=l||(l={})).User="User",m.Admin="Admin",m.Supervisor="Supervisor",m.Staff="Staff"}},function(e){e.O(0,[301,971,117,744],function(){return e(e.s=7573)}),_N_E=e.O()}]);