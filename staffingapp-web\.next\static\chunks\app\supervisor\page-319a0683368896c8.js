(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[511],{7508:function(e,s,t){Promise.resolve().then(t.bind(t,8879))},9376:function(e,s,t){"use strict";var a=t(5475);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}})},8879:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var a=t(7437),l=t(2265),r=t(9376);let n=e=>e.replace(/([A-Z])/g," $1").trim();function i(){let e=(0,r.useRouter)(),[s,t]=(0,l.useState)(!0),[i,d]=(0,l.useState)(null),[c,o]=(0,l.useState)([]),[m,x]=(0,l.useState)([]),[h,g]=(0,l.useState)("pending"),[u,f]=(0,l.useState)(!1),[j,p]=(0,l.useState)(null),[b,N]=(0,l.useState)({staffId:"",notes:""});(0,l.useEffect)(()=>{(()=>{let s=sessionStorage.getItem("token"),t=sessionStorage.getItem("user");if(!s||!t){e.push("/login");return}let a=JSON.parse(t);if("Supervisor"!==a.role){e.push("/dashboard");return}d(a),y(),v()})()},[e]);let y=async()=>{try{let e=sessionStorage.getItem("token"),s=await fetch("http://localhost:5000/api/jobs/all",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(s.ok){let e=await s.json();o(e)}else console.error("Failed to fetch jobs:",s.status,s.statusText)}catch(e){console.error("Error fetching jobs:",e)}finally{t(!1)}},v=async()=>{try{let e=sessionStorage.getItem("token"),s=await fetch("http://localhost:5000/api/auth/users/staff",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(s.ok){let e=await s.json();x(e)}else console.error("Failed to fetch staff members:",s.status,s.statusText)}catch(e){console.error("Error fetching staff members:",e)}},w=e=>{p(e),f(!0),N({staffId:"",notes:""})},S=async e=>{try{let s=sessionStorage.getItem("token");(await fetch("http://localhost:5000/api/jobs/".concat(e,"/reject"),{method:"PUT",headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"}})).ok&&y()}catch(e){console.error("Error rejecting job:",e)}},k=async()=>{if(j&&b.staffId)try{let e=sessionStorage.getItem("token");(await fetch("http://localhost:5000/api/jobs/".concat(j.id,"/assign"),{method:"PUT",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"},body:JSON.stringify({staffId:parseInt(b.staffId),notes:b.notes})})).ok&&(f(!1),p(null),N({staffId:"",notes:""}),y())}catch(e){console.error("Error assigning job:",e)}},A=e=>c.filter(s=>s.status===e),I=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});if(s)return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading supervisor dashboard..."})]})});let C=A("Pending");A("UnderReview");let T=A("Assigned"),P=A("InProgress");return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"Staff Hall - Supervisor Dashboard"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",null==i?void 0:i.firstName," ",null==i?void 0:i.lastName]}),(0,a.jsx)("button",{onClick:()=>{sessionStorage.removeItem("token"),sessionStorage.removeItem("user"),e.push("/login")},className:"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Logout"})]})]})})}),(0,a.jsx)("main",{className:"pt-24 py-10",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:C.length})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Pending Review"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[C.length," jobs"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:T.length})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Assigned"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[T.length," jobs"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:P.length})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"In Progress"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[P.length," jobs"]})]})})]})})}),(0,a.jsx)("div",{className:"bg-white overflow-hidden shadow rounded-lg",children:(0,a.jsx)("div",{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:m.length})})}),(0,a.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,a.jsxs)("dl",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:"Staff Members"}),(0,a.jsxs)("dd",{className:"text-lg font-medium text-gray-900",children:[m.length," active"]})]})})]})})})]}),(0,a.jsxs)("div",{className:"bg-white shadow rounded-lg",children:[(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"pending",name:"Pending Review",count:C.length},{id:"assigned",name:"Assigned Jobs",count:T.length},{id:"progress",name:"In Progress",count:P.length},{id:"staff",name:"Staff Management",count:m.length}].map(e=>(0,a.jsxs)("button",{onClick:()=>g(e.id),className:"".concat(h===e.id?"border-emerald-500 text-emerald-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"," whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"),children:[e.name,(0,a.jsx)("span",{className:"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs",children:e.count})]},e.id))})}),(0,a.jsxs)("div",{className:"p-6",children:["pending"===h&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs Pending Review"}),0===C.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No pending jobs to review"}):(0,a.jsx)("div",{className:"space-y-4",children:C.map(s=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",n(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",n(s.category)]}),(0,a.jsxs)("span",{children:["Submitted: ",I(s.createdAt)]})]})]}),(0,a.jsxs)("div",{className:"ml-4 flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e.push("/jobs/".concat(s.id)),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),(0,a.jsx)("button",{onClick:()=>w(s),className:"bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-1 rounded text-sm",children:"Assign"}),(0,a.jsx)("button",{onClick:()=>S(s.id),className:"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm",children:"Reject"})]})]})},s.id))})]}),"assigned"===h&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Assigned Jobs"}),0===T.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No assigned jobs"}):(0,a.jsx)("div",{className:"space-y-4",children:T.map(s=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 bg-purple-50",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",n(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",n(s.category)]}),(0,a.jsxs)("span",{children:["Assigned: ",s.assignedAt?I(s.assignedAt):"N/A"]})]}),s.assignedTo&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Assigned to: ",(0,a.jsxs)("strong",{children:[s.assignedTo.firstName," ",s.assignedTo.lastName]})]})}),s.supervisorNotes&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-blue-50 rounded",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:"Notes:"})," ",s.supervisorNotes]})})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e.push("/jobs/".concat(s.id)),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),(0,a.jsx)("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-purple-100 text-purple-800",children:n(s.status)})]})]})},s.id))})]}),"progress"===h&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Jobs In Progress"}),0===P.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No jobs in progress"}):(0,a.jsx)("div",{className:"space-y-4",children:P.map(s=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 bg-emerald-50",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:s.title}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:s.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",n(s.jobType)]}),(0,a.jsxs)("span",{children:["Category: ",n(s.category)]}),(0,a.jsxs)("span",{children:["Started: ",s.assignedAt?I(s.assignedAt):"N/A"]})]}),s.assignedTo&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Being worked on by: ",(0,a.jsxs)("strong",{children:[s.assignedTo.firstName," ",s.assignedTo.lastName]})]})})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>e.push("/jobs/".concat(s.id)),className:"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"View Details"}),(0,a.jsx)("span",{className:"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-emerald-100 text-emerald-800",children:n(s.status)})]})]})},s.id))})]}),"staff"===h&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Staff Members"}),0===m.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No staff members found"}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:m.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:e.email}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800",children:n(e.role)})})]},e.id))})]})]})]})]})}),u&&j&&(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",children:(0,a.jsx)("div",{className:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white",children:(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:["Assign Job: ",j.title]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Assign to Staff Member"}),(0,a.jsxs)("select",{value:b.staffId,onChange:e=>N({...b,staffId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select staff member..."}),m.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.firstName," ",e.lastName," (",e.email,")"]},e.id))]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Notes for Staff Member (Optional)"}),(0,a.jsx)("textarea",{value:b.notes,onChange:e=>N({...b,notes:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500",rows:3,placeholder:"Add any special instructions or notes..."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{f(!1),p(null),N({staffId:"",notes:""})},className:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium",children:"Cancel"}),(0,a.jsx)("button",{onClick:k,disabled:!b.staffId,className:"bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium",children:"Assign Job"})]})]})})})]})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=7508)}),_N_E=e.O()}]);