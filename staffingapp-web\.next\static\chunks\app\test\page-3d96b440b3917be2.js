(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[928],{399:function(e,s,n){Promise.resolve().then(n.bind(n,9742))},9742:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return o}});var l=n(7437),r=n(2265);function o(){let[e,s]=(0,r.useState)("");return(0,r.useEffect)(()=>{s(new Date().toLocaleString())},[]),(0,l.jsx)("div",{className:"min-h-screen bg-gray-100 p-8",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsxs)("div",{className:"bg-red-500 text-white p-8 rounded-lg mb-8",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold mb-4",children:"\uD83D\uDD34 FRONTEND TEST PAGE"}),(0,l.jsx)("p",{className:"text-xl",children:"If you can see this, the Next.js frontend is working correctly!"}),e&&(0,l.jsxs)("p",{className:"mt-2",children:["Current time: ",e]})]}),(0,l.jsxs)("div",{className:"bg-blue-500 text-white p-8 rounded-lg mb-8",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"\uD83D\uDD35 Navigation Test"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("a",{href:"/login",className:"block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded",children:"Go to Login Page"}),(0,l.jsx)("a",{href:"/dashboard",className:"block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded",children:"Go to Dashboard Page"}),(0,l.jsx)("a",{href:"/submit-job",className:"block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded",children:"Go to Submit Job Page"})]})]}),(0,l.jsxs)("div",{className:"bg-green-500 text-white p-8 rounded-lg",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"\uD83D\uDFE2 Instructions"}),(0,l.jsxs)("ol",{className:"list-decimal list-inside space-y-2",children:[(0,l.jsx)("li",{children:"If you can see this page, the frontend is working"}),(0,l.jsxs)("li",{children:['Click "Go to Login Page" and log in with: ',(0,l.jsx)("strong",{children:"<EMAIL>"})," / ",(0,l.jsx)("strong",{children:"password123"})]}),(0,l.jsx)("li",{children:'After logging in, click "Go to Dashboard Page"'}),(0,l.jsx)("li",{children:"You should see the red test box on the dashboard"})]}),(0,l.jsxs)("div",{className:"mt-4 p-4 bg-green-600 rounded",children:[(0,l.jsx)("h3",{className:"font-bold",children:"Correct Login Credentials:"}),(0,l.jsxs)("p",{children:["Email: ",(0,l.jsx)("code",{className:"bg-green-700 px-2 py-1 rounded",children:"<EMAIL>"})]}),(0,l.jsxs)("p",{children:["Password: ",(0,l.jsx)("code",{className:"bg-green-700 px-2 py-1 rounded",children:"password123"})]})]})]})]})})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=399)}),_N_E=e.O()}]);