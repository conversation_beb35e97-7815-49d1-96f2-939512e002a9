"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/submit-job/page",{

/***/ "(app-pages-browser)/./src/types/models.ts":
/*!*****************************!*\
  !*** ./src/types/models.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JobCategory: function() { return /* binding */ JobCategory; },\n/* harmony export */   JobStatus: function() { return /* binding */ JobStatus; },\n/* harmony export */   JobType: function() { return /* binding */ JobType; },\n/* harmony export */   OutputFormat: function() { return /* binding */ OutputFormat; },\n/* harmony export */   UserRole: function() { return /* binding */ UserRole; }\n/* harmony export */ });\nvar JobType;\n(function(JobType) {\n    JobType[\"DataEntry\"] = \"DataEntry\";\n    JobType[\"Accounting\"] = \"Accounting\";\n    JobType[\"HR\"] = \"HR\";\n    JobType[\"ITSupport\"] = \"ITSupport\";\n    JobType[\"Marketing\"] = \"Marketing\";\n    JobType[\"Legal\"] = \"Legal\";\n    JobType[\"CustomerService\"] = \"CustomerService\";\n    JobType[\"Other\"] = \"Other\";\n})(JobType || (JobType = {}));\nvar JobCategory;\n(function(JobCategory) {\n    // Data Entry Categories\n    JobCategory[\"DataProcessing\"] = \"DataProcessing\";\n    JobCategory[\"DataCleaning\"] = \"DataCleaning\";\n    JobCategory[\"DocumentationEntry\"] = \"DocumentationEntry\";\n    // Accounting Categories\n    JobCategory[\"Bookkeeping\"] = \"Bookkeeping\";\n    JobCategory[\"FinancialReporting\"] = \"FinancialReporting\";\n    JobCategory[\"Taxation\"] = \"Taxation\";\n    JobCategory[\"Payroll\"] = \"Payroll\";\n    // HR Categories\n    JobCategory[\"Recruitment\"] = \"Recruitment\";\n    JobCategory[\"EmployeeRelations\"] = \"EmployeeRelations\";\n    JobCategory[\"Training\"] = \"Training\";\n    JobCategory[\"CompensationBenefits\"] = \"CompensationBenefits\";\n    // IT Support Categories\n    JobCategory[\"TechnicalSupport\"] = \"TechnicalSupport\";\n    JobCategory[\"NetworkSupport\"] = \"NetworkSupport\";\n    JobCategory[\"SoftwareSupport\"] = \"SoftwareSupport\";\n    JobCategory[\"HardwareSupport\"] = \"HardwareSupport\";\n    // Marketing Categories\n    JobCategory[\"DigitalMarketing\"] = \"DigitalMarketing\";\n    JobCategory[\"ContentCreation\"] = \"ContentCreation\";\n    JobCategory[\"SocialMedia\"] = \"SocialMedia\";\n    JobCategory[\"MarketResearch\"] = \"MarketResearch\";\n    // Legal Categories\n    JobCategory[\"ContractReview\"] = \"ContractReview\";\n    JobCategory[\"Compliance\"] = \"Compliance\";\n    JobCategory[\"LegalResearch\"] = \"LegalResearch\";\n    JobCategory[\"Documentation\"] = \"Documentation\";\n    // Customer Service Categories\n    JobCategory[\"CallCenter\"] = \"CallCenter\";\n    JobCategory[\"EmailSupport\"] = \"EmailSupport\";\n    JobCategory[\"ChatSupport\"] = \"ChatSupport\";\n    JobCategory[\"CustomerFeedback\"] = \"CustomerFeedback\";\n    // Other\n    JobCategory[\"Other\"] = \"Other\";\n})(JobCategory || (JobCategory = {}));\nvar OutputFormat;\n(function(OutputFormat) {\n    OutputFormat[\"PDF\"] = \"PDF\";\n    OutputFormat[\"Word\"] = \"Word\";\n    OutputFormat[\"Excel\"] = \"Excel\";\n    OutputFormat[\"PlainText\"] = \"PlainText\";\n    OutputFormat[\"JSON\"] = \"JSON\";\n    OutputFormat[\"XML\"] = \"XML\";\n    OutputFormat[\"Database\"] = \"Database\";\n    OutputFormat[\"Other\"] = \"Other\";\n})(OutputFormat || (OutputFormat = {}));\nvar JobStatus;\n(function(JobStatus) {\n    JobStatus[\"Pending\"] = \"Pending\";\n    JobStatus[\"UnderReview\"] = \"UnderReview\";\n    JobStatus[\"Assigned\"] = \"Assigned\";\n    JobStatus[\"InProgress\"] = \"InProgress\";\n    JobStatus[\"Completed\"] = \"Completed\";\n    JobStatus[\"UnderSupervisorReview\"] = \"UnderSupervisorReview\";\n    JobStatus[\"Delivered\"] = \"Delivered\";\n    JobStatus[\"Satisfied\"] = \"Satisfied\";\n    JobStatus[\"Cancelled\"] = \"Cancelled\";\n    JobStatus[\"OnHold\"] = \"OnHold\";\n    JobStatus[\"Rejected\"] = \"Rejected\";\n})(JobStatus || (JobStatus = {}));\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"User\"] = \"User\";\n    UserRole[\"Admin\"] = \"Admin\";\n    UserRole[\"Supervisor\"] = \"Supervisor\";\n    UserRole[\"Staff\"] = \"Staff\";\n})(UserRole || (UserRole = {}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/models.ts\n"));

/***/ })

});