"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/supervisor/page",{

/***/ "(app-pages-browser)/./src/app/supervisor/page.tsx":
/*!*************************************!*\
  !*** ./src/app/supervisor/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SupervisorDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Utility function to format enum names with proper spacing\nconst formatEnumName = (enumValue)=>{\n    return enumValue.replace(/([A-Z])/g, \" $1\").trim();\n};\nfunction SupervisorDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [staffMembers, setStaffMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pending\");\n    const [showAssignModal, setShowAssignModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedJob, setSelectedJob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assignmentData, setAssignmentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        staffId: \"\",\n        notes: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = ()=>{\n            const token = sessionStorage.getItem(\"token\");\n            const userStr = sessionStorage.getItem(\"user\");\n            if (!token || !userStr) {\n                router.push(\"/login\");\n                return;\n            }\n            const userData = JSON.parse(userStr);\n            if (userData.role !== \"Supervisor\") {\n                router.push(\"/dashboard\");\n                return;\n            }\n            setUser(userData);\n            fetchJobs();\n            fetchStaffMembers();\n        };\n        checkAuth();\n    }, [\n        router\n    ]);\n    const fetchJobs = async ()=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/jobs/all\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const jobsData = await response.json();\n                console.log(\"Fetched jobs data:\", jobsData);\n                console.log(\"Jobs count:\", jobsData.length);\n                if (jobsData.length > 0) {\n                    console.log(\"First job:\", jobsData[0]);\n                    console.log(\"Job statuses:\", jobsData.map((job)=>job.status));\n                }\n                setJobs(jobsData);\n            } else {\n                console.error(\"Failed to fetch jobs:\", response.status, response.statusText);\n            }\n        } catch (error) {\n            console.error(\"Error fetching jobs:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchStaffMembers = async ()=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/auth/users/staff\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const staffData = await response.json();\n                setStaffMembers(staffData);\n            } else {\n                console.error(\"Failed to fetch staff members:\", response.status, response.statusText);\n            }\n        } catch (error) {\n            console.error(\"Error fetching staff members:\", error);\n        }\n    };\n    const handleLogout = ()=>{\n        sessionStorage.clear();\n        document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n        window.location.href = \"/login\";\n    };\n    const handleAssignJob = (job)=>{\n        setSelectedJob(job);\n        setShowAssignModal(true);\n        setAssignmentData({\n            staffId: \"\",\n            notes: \"\"\n        });\n    };\n    const handleRejectJob = async (jobId)=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/jobs/\".concat(jobId, \"/reject\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                fetchJobs(); // Refresh the jobs list\n            }\n        } catch (error) {\n            console.error(\"Error rejecting job:\", error);\n        }\n    };\n    const submitAssignment = async ()=>{\n        if (!selectedJob || !assignmentData.staffId) return;\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/jobs/\".concat(selectedJob.id, \"/assign\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    staffId: parseInt(assignmentData.staffId),\n                    notes: assignmentData.notes\n                })\n            });\n            if (response.ok) {\n                setShowAssignModal(false);\n                setSelectedJob(null);\n                setAssignmentData({\n                    staffId: \"\",\n                    notes: \"\"\n                });\n                fetchJobs(); // Refresh the jobs list\n            }\n        } catch (error) {\n            console.error(\"Error assigning job:\", error);\n        }\n    };\n    const getJobsByStatus = (status)=>{\n        return jobs.filter((job)=>job.status === status);\n    };\n    const getStatusColor = (status)=>{\n        const colors = {\n            \"Pending\": \"bg-yellow-100 text-yellow-800\",\n            \"UnderReview\": \"bg-blue-100 text-blue-800\",\n            \"Assigned\": \"bg-purple-100 text-purple-800\",\n            \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n            \"Completed\": \"bg-green-100 text-green-800\",\n            \"Delivered\": \"bg-gray-100 text-gray-800\",\n            \"Rejected\": \"bg-red-100 text-red-800\"\n        };\n        return colors[status] || \"bg-gray-100 text-gray-800\";\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading supervisor dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, this);\n    }\n    const pendingJobs = getJobsByStatus(\"Pending\");\n    const underReviewJobs = getJobsByStatus(\"UnderReview\");\n    const assignedJobs = getJobsByStatus(\"Assigned\");\n    const inProgressJobs = getJobsByStatus(\"InProgress\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Staff Hall - Supervisor Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: [\n                                            \"Welcome, \",\n                                            user === null || user === void 0 ? void 0 : user.firstName,\n                                            \" \",\n                                            user === null || user === void 0 ? void 0 : user.lastName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: pendingJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: \"Pending Review\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    pendingJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: assignedJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: \"Assigned\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    assignedJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: inProgressJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: \"In Progress\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    inProgressJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: staffMembers.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: \"Staff Members\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    staffMembers.length,\n                                                                    \" active\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"-mb-px flex space-x-8 px-6\",\n                                        \"aria-label\": \"Tabs\",\n                                        children: [\n                                            {\n                                                id: \"pending\",\n                                                name: \"Pending Review\",\n                                                count: pendingJobs.length\n                                            },\n                                            {\n                                                id: \"assigned\",\n                                                name: \"Assigned Jobs\",\n                                                count: assignedJobs.length\n                                            },\n                                            {\n                                                id: \"progress\",\n                                                name: \"In Progress\",\n                                                count: inProgressJobs.length\n                                            },\n                                            {\n                                                id: \"staff\",\n                                                name: \"Staff Management\",\n                                                count: staffMembers.length\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"\".concat(activeTab === tab.id ? \"border-emerald-500 text-emerald-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\"),\n                                                children: [\n                                                    tab.name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs\",\n                                                        children: tab.count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        activeTab === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Jobs Pending Review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 19\n                                                }, this),\n                                                pendingJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No pending jobs to review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: pendingJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 344,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 346,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 347,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Submitted: \",\n                                                                                            formatDate(job.createdAt)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 348,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 345,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 352,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAssignJob(job),\n                                                                                className: \"bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"Assign\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleRejectJob(job.id),\n                                                                                className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"Reject\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 351,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"assigned\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Assigned Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 19\n                                                }, this),\n                                                assignedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No assigned jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: assignedJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4 bg-purple-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 390,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 391,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 393,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 394,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Assigned: \",\n                                                                                            job.assignedAt ? formatDate(job.assignedAt) : \"N/A\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 395,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            job.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Assigned to: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: [\n                                                                                                job.assignedTo.firstName,\n                                                                                                \" \",\n                                                                                                job.assignedTo.lastName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 400,\n                                                                                            columnNumber: 50\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 399,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 398,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            job.supervisorNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 p-2 bg-blue-50 rounded\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-blue-800\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: \"Notes:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 407,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        job.supervisorNotes\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 406,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 405,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 389,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 413,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-purple-100 text-purple-800\",\n                                                                                children: formatEnumName(job.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 419,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"progress\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Jobs In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, this),\n                                                inProgressJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No jobs in progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: inProgressJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4 bg-emerald-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 442,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 443,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 445,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 446,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Started: \",\n                                                                                            job.assignedAt ? formatDate(job.assignedAt) : \"N/A\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 447,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 444,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            job.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Being worked on by: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: [\n                                                                                                job.assignedTo.firstName,\n                                                                                                \" \",\n                                                                                                job.assignedTo.lastName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 452,\n                                                                                            columnNumber: 57\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 450,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 441,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 458,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-emerald-100 text-emerald-800\",\n                                                                                children: formatEnumName(job.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 464,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"staff\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Staff Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, this),\n                                                staffMembers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No staff members found\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                    children: staffMembers.map((staff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        staff.firstName,\n                                                                        \" \",\n                                                                        staff.lastName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 text-sm\",\n                                                                    children: staff.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800\",\n                                                                        children: formatEnumName(staff.role)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 487,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, staff.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            showAssignModal && selectedJob && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"Assign Job: \",\n                                    selectedJob.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Assign to Staff Member\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: assignmentData.staffId,\n                                        onChange: (e)=>setAssignmentData({\n                                                ...assignmentData,\n                                                staffId: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                        required: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select staff member...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 19\n                                            }, this),\n                                            staffMembers.map((staff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: staff.id,\n                                                    children: [\n                                                        staff.firstName,\n                                                        \" \",\n                                                        staff.lastName,\n                                                        \" (\",\n                                                        staff.email,\n                                                        \")\"\n                                                    ]\n                                                }, staff.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Notes for Staff Member (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: assignmentData.notes,\n                                        onChange: (e)=>setAssignmentData({\n                                                ...assignmentData,\n                                                notes: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                        rows: 3,\n                                        placeholder: \"Add any special instructions or notes...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowAssignModal(false);\n                                            setSelectedJob(null);\n                                            setAssignmentData({\n                                                staffId: \"\",\n                                                notes: \"\"\n                                            });\n                                        },\n                                        className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: submitAssignment,\n                                        disabled: !assignmentData.staffId,\n                                        className: \"bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Assign Job\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 555,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 505,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(SupervisorDashboard, \"ZjmaA0bAJNPdOUQmPTNhmWo1FkQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SupervisorDashboard;\nvar _c;\n$RefreshReg$(_c, \"SupervisorDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/supervisor/page.tsx\n"));

/***/ })

});