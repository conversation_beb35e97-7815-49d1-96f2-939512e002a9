'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { JobType, JobCategory } from '@/types/models';

// Utility function to format enum names with proper spacing
const formatEnumName = (enumValue: string): string => {
  return enumValue.replace(/([A-Z])/g, ' $1').trim();
};

// Mapping of job types to their related categories
const jobTypeCategoryMapping: Record<JobType, JobCategory[]> = {
  [JobType.DataEntry]: [
    JobCategory.DataProcessing,
    JobCategory.DataCleaning,
    JobCategory.DocumentationEntry
  ],
  [JobType.Accounting]: [
    JobCategory.Bookkeeping,
    JobCategory.FinancialReporting,
    JobCategory.Taxation,
    JobCategory.Payroll
  ],
  [JobType.HR]: [
    JobCategory.Recruitment,
    JobCategory.EmployeeRelations,
    JobCategory.Training,
    JobCategory.CompensationBenefits
  ],
  [JobType.ITSupport]: [
    JobCategory.TechnicalSupport,
    JobCategory.NetworkSupport,
    JobCategory.SoftwareSupport,
    JobCategory.HardwareSupport
  ],
  [JobType.Marketing]: [
    JobCategory.DigitalMarketing,
    JobCategory.ContentCreation,
    JobCategory.SocialMedia,
    JobCategory.MarketResearch
  ],
  [JobType.Legal]: [
    JobCategory.ContractReview,
    JobCategory.Compliance,
    JobCategory.LegalResearch,
    JobCategory.Documentation
  ],
  [JobType.CustomerService]: [
    JobCategory.CallCenter,
    JobCategory.EmailSupport,
    JobCategory.ChatSupport,
    JobCategory.CustomerFeedback
  ],
  [JobType.Other]: [
    JobCategory.Other
  ]
};

// Function to get job type for a category
const getJobTypeForCategory = (category: JobCategory): JobType => {
  for (const [jobType, categories] of Object.entries(jobTypeCategoryMapping)) {
    if (categories.includes(category)) {
      return jobType as JobType;
    }
  }
  return JobType.Other;
};

export default function AdminDashboard() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Check authentication and admin role
    const checkAdminAccess = () => {
      const token = sessionStorage.getItem('token');
      const userStr = sessionStorage.getItem('user');

      if (!token || !userStr) {
        router.push('/login');
        return;
      }

      const userData = JSON.parse(userStr);
      
      // Check if user has admin role (you'll need to add this to your user model)
      // Temporarily disabled for testing - uncomment in production
      // if (userData.role !== 'admin') {
      //   router.push('/dashboard'); // Redirect non-admin users
      //   return;
      // }

      setUser(userData);
      setIsLoading(false);
    };

    checkAdminAccess();
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100">
        <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex">
                <div className="flex-shrink-0 flex items-center">
                  <span className="text-xl font-bold text-emerald-600">Staff Hall Admin</span>
                </div>
              </div>
            </div>
          </div>
        </nav>
        
        <main className="pt-24 py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'job-types', name: 'Job Types', icon: '📝' },
    { id: 'categories', name: 'Categories', icon: '📂' },
    { id: 'rates', name: 'Hour Rates', icon: '💰' },
    { id: 'bonuses', name: 'Bonus Limits', icon: '🎁' },
    { id: 'users', name: 'User Management', icon: '👥' },
    { id: 'settings', name: 'Settings', icon: '⚙️' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewTab />;
      case 'job-types':
        return <JobTypesTab />;
      case 'categories':
        return <CategoriesTab />;
      case 'rates':
        return <RatesTab />;
      case 'bonuses':
        return <BonusesTab />;
      case 'users':
        return <UsersTab />;
      case 'settings':
        return <SettingsTab />;
      default:
        return <OverviewTab />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 bg-white shadow-sm z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <span className="text-xl font-bold text-emerald-600">Staff Hall Admin</span>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user?.firstName}</span>
              <a
                href="/dashboard"
                className="text-emerald-600 hover:text-emerald-800 text-sm font-medium"
              >
                User Dashboard
              </a>
              <button
                type="button"
                className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500"
                onClick={() => {
                  sessionStorage.clear();
                  document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                  window.location.href = '/login';
                }}
              >
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main className="pt-24 py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="mt-2 text-gray-600">Manage your Staff Hall application settings and configurations</p>
          </div>

          {/* Tabs */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                      activeTab === tab.id
                        ? 'border-emerald-500 text-emerald-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="bg-white shadow rounded-lg">
            {renderTabContent()}
          </div>
        </div>
      </main>
    </div>
  );
}

// Overview Tab Component
function OverviewTab() {
  return (
    <div className="p-6">
      <h2 className="text-lg font-medium text-gray-900 mb-6">System Overview</h2>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-emerald-50 p-6 rounded-xl border border-emerald-200">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4">
              <span className="text-white text-xl">👥</span>
            </div>
            <div>
              <div className="text-sm font-medium text-emerald-700">Total Users</div>
              <div className="text-3xl font-bold text-emerald-900">156</div>
            </div>
          </div>
        </div>
        
        <div className="bg-emerald-50 p-6 rounded-xl border border-emerald-200">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4">
              <span className="text-white text-xl">📝</span>
            </div>
            <div>
              <div className="text-sm font-medium text-emerald-700">Active Jobs</div>
              <div className="text-3xl font-bold text-emerald-900">42</div>
            </div>
          </div>
        </div>
        
        <div className="bg-emerald-50 p-6 rounded-xl border border-emerald-200">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4">
              <span className="text-white text-xl">💰</span>
            </div>
            <div>
              <div className="text-sm font-medium text-emerald-700">Revenue</div>
              <div className="text-3xl font-bold text-emerald-900">$12,450</div>
            </div>
          </div>
        </div>
        
        <div className="bg-emerald-50 p-6 rounded-xl border border-emerald-200">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center mr-4">
              <span className="text-white text-xl">⏱️</span>
            </div>
            <div>
              <div className="text-sm font-medium text-emerald-700">Hours Sold</div>
              <div className="text-3xl font-bold text-emerald-900">2,340</div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-4">
          <div className="flex items-center p-4 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3">
              <span className="text-white text-sm">👤</span>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">New user registered</p>
              <p className="text-xs text-gray-500">John Doe joined as Individual account</p>
            </div>
            <span className="text-xs text-gray-400">2 minutes ago</span>
          </div>
          
          <div className="flex items-center p-4 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3">
              <span className="text-white text-sm">📝</span>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Job completed</p>
              <p className="text-xs text-gray-500">Data Entry task finished by ABC Corp</p>
            </div>
            <span className="text-xs text-gray-400">15 minutes ago</span>
          </div>
          
          <div className="flex items-center p-4 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center mr-3">
              <span className="text-white text-sm">💰</span>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Hours purchased</p>
              <p className="text-xs text-gray-500">Jane Smith bought 50 hours</p>
            </div>
            <span className="text-xs text-gray-400">1 hour ago</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Job Types Management Tab
function JobTypesTab() {
  // Initialize job types from the JobType enum
  const [jobTypes, setJobTypes] = useState(() => {
    return Object.values(JobType).map((jobType, index) => ({
      id: index + 1,
      name: jobType,
      description: `${formatEnumName(jobType)} tasks and services`,
      isActive: true
    }));
  });
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingType, setEditingType] = useState<any>(null);
  const [formData, setFormData] = useState({ name: '', description: '', isActive: true });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingType) {
      // Update existing
      setJobTypes(jobTypes.map(type =>
        type.id === editingType.id
          ? { ...type, ...formData }
          : type
      ));
      setEditingType(null);
    } else {
      // Add new
      const newType = {
        id: Math.max(...jobTypes.map(t => t.id)) + 1,
        ...formData
      };
      setJobTypes([...jobTypes, newType]);
    }
    setFormData({ name: '', description: '', isActive: true });
    setShowAddForm(false);
  };

  const handleEdit = (type: any) => {
    setEditingType(type);
    setFormData({ name: type.name, description: type.description, isActive: type.isActive });
    setShowAddForm(true);
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this job type?')) {
      setJobTypes(jobTypes.filter(type => type.id !== id));
    }
  };

  const toggleStatus = (id: number) => {
    setJobTypes(jobTypes.map(type =>
      type.id === id ? { ...type, isActive: !type.isActive } : type
    ));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-medium text-gray-900">Job Types Management</h2>
        <button
          onClick={() => {
            setShowAddForm(true);
            setEditingType(null);
            setFormData({ name: '', description: '', isActive: true });
          }}
          className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          Add Job Type
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <h3 className="text-md font-medium text-emerald-800 mb-4">
            {editingType ? 'Edit Job Type' : 'Add New Job Type'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Job Type Name</label>
                <select
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="">Select a job type...</option>
                  {Object.values(JobType).map((jobType) => (
                    <option key={jobType} value={jobType}>
                      {formatEnumName(jobType)}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={formData.isActive.toString()}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.value === 'true' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                required
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                rows={3}
                placeholder="Describe this job type..."
              />
            </div>
            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                {editingType ? 'Update' : 'Add'} Job Type
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setEditingType(null);
                  setFormData({ name: '', description: '', isActive: true });
                }}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Job Types List */}
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {jobTypes.map((type) => (
              <tr key={type.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {formatEnumName(type.name)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">{type.description}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleStatus(type.id)}
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      type.isActive
                        ? 'bg-emerald-100 text-emerald-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {type.isActive ? 'Active' : 'Inactive'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    onClick={() => handleEdit(type)}
                    className="text-emerald-600 hover:text-emerald-900"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(type.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// Categories Management Tab
function CategoriesTab() {
  // Initialize categories from the JobCategory enum with their job type relationships
  const [categories, setCategories] = useState(() => {
    return Object.values(JobCategory).map((category, index) => ({
      id: index + 1,
      name: category,
      jobType: getJobTypeForCategory(category),
      description: `${formatEnumName(category)} tasks and services`,
      isActive: true
    }));
  });
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<any>(null);
  const [formData, setFormData] = useState({ name: '', jobType: JobType.DataEntry, description: '', isActive: true });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingCategory) {
      setCategories(categories.map(cat =>
        cat.id === editingCategory.id
          ? { ...cat, ...formData }
          : cat
      ));
      setEditingCategory(null);
    } else {
      const newCategory = {
        id: Math.max(...categories.map(c => c.id)) + 1,
        ...formData
      };
      setCategories([...categories, newCategory]);
    }
    setFormData({ name: '', jobType: JobType.DataEntry, description: '', isActive: true });
    setShowAddForm(false);
  };

  const handleEdit = (category: any) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      jobType: category.jobType,
      description: category.description,
      isActive: category.isActive
    });
    setShowAddForm(true);
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this category?')) {
      setCategories(categories.filter(cat => cat.id !== id));
    }
  };

  const toggleStatus = (id: number) => {
    setCategories(categories.map(cat =>
      cat.id === id ? { ...cat, isActive: !cat.isActive } : cat
    ));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-medium text-gray-900">Categories Management</h2>
        <button
          onClick={() => {
            setShowAddForm(true);
            setEditingCategory(null);
            setFormData({ name: '', jobType: JobType.DataEntry, description: '', isActive: true });
          }}
          className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          Add Category
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <h3 className="text-md font-medium text-emerald-800 mb-4">
            {editingCategory ? 'Edit Category' : 'Add New Category'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category Name</label>
                <select
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({
                    ...formData,
                    name: e.target.value,
                    jobType: getJobTypeForCategory(e.target.value as JobCategory)
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="">Select a category...</option>
                  {Object.values(JobCategory).map((category) => (
                    <option key={category} value={category}>
                      {formatEnumName(category)}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Job Type</label>
                <input
                  type="text"
                  value={formatEnumName(formData.jobType)}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
                  placeholder="Auto-assigned based on category"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={formData.isActive.toString()}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.value === 'true' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                required
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                rows={3}
                placeholder="Describe this category..."
              />
            </div>
            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                {editingCategory ? 'Update' : 'Add'} Category
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setEditingCategory(null);
                  setFormData({ name: '', jobType: JobType.DataEntry, description: '', isActive: true });
                }}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Categories List */}
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job Type</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {categories.map((category) => (
              <tr key={category.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {formatEnumName(category.name)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                  <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                    {formatEnumName(category.jobType)}
                  </span>
                </td>
                <td className="px-6 py-4 text-sm text-gray-500">{category.description}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleStatus(category.id)}
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      category.isActive
                        ? 'bg-emerald-100 text-emerald-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {category.isActive ? 'Active' : 'Inactive'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    onClick={() => handleEdit(category)}
                    className="text-emerald-600 hover:text-emerald-900"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(category.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// Hour Rates Management Tab
function RatesTab() {
  const [rates, setRates] = useState([
    { id: 1, packageName: 'Basic Package', hours: 10, price: 50, pricePerHour: 5.0, isActive: true },
    { id: 2, packageName: 'Standard Package', hours: 25, price: 100, pricePerHour: 4.0, isActive: true },
    { id: 3, packageName: 'Premium Package', hours: 50, price: 180, pricePerHour: 3.6, isActive: true },
    { id: 4, packageName: 'Enterprise Package', hours: 100, price: 320, pricePerHour: 3.2, isActive: true }
  ]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingRate, setEditingRate] = useState<any>(null);
  const [formData, setFormData] = useState({ packageName: '', hours: 0, price: 0, isActive: true });

  const calculatePricePerHour = (price: number, hours: number) => {
    return hours > 0 ? (price / hours).toFixed(2) : '0.00';
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const pricePerHour = parseFloat(calculatePricePerHour(formData.price, formData.hours));

    if (editingRate) {
      setRates(rates.map(rate =>
        rate.id === editingRate.id
          ? { ...rate, ...formData, pricePerHour }
          : rate
      ));
      setEditingRate(null);
    } else {
      const newRate = {
        id: Math.max(...rates.map(r => r.id)) + 1,
        ...formData,
        pricePerHour
      };
      setRates([...rates, newRate]);
    }
    setFormData({ packageName: '', hours: 0, price: 0, isActive: true });
    setShowAddForm(false);
  };

  const handleEdit = (rate: any) => {
    setEditingRate(rate);
    setFormData({
      packageName: rate.packageName,
      hours: rate.hours,
      price: rate.price,
      isActive: rate.isActive
    });
    setShowAddForm(true);
  };

  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this rate package?')) {
      setRates(rates.filter(rate => rate.id !== id));
    }
  };

  const toggleStatus = (id: number) => {
    setRates(rates.map(rate =>
      rate.id === id ? { ...rate, isActive: !rate.isActive } : rate
    ));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-medium text-gray-900">Hour Rates Management</h2>
        <button
          onClick={() => {
            setShowAddForm(true);
            setEditingRate(null);
            setFormData({ packageName: '', hours: 0, price: 0, isActive: true });
          }}
          className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          Add Rate Package
        </button>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <div className="mb-6 bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <h3 className="text-md font-medium text-emerald-800 mb-4">
            {editingRate ? 'Edit Rate Package' : 'Add New Rate Package'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Package Name</label>
                <input
                  type="text"
                  required
                  value={formData.packageName}
                  onChange={(e) => setFormData({ ...formData, packageName: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="e.g., Basic Package"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Hours</label>
                <input
                  type="number"
                  required
                  min="1"
                  value={formData.hours || ''}
                  onChange={(e) => setFormData({ ...formData, hours: parseInt(e.target.value) || 0 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="10"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Price ($)</label>
                <input
                  type="number"
                  required
                  min="0"
                  step="0.01"
                  value={formData.price || ''}
                  onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  placeholder="50.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={formData.isActive.toString()}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.value === 'true' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                >
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>
            {formData.hours > 0 && formData.price > 0 && (
              <div className="bg-gray-50 p-3 rounded-md">
                <p className="text-sm text-gray-600">
                  Price per hour: <span className="font-medium">${calculatePricePerHour(formData.price, formData.hours)}</span>
                </p>
              </div>
            )}
            <div className="flex space-x-3">
              <button
                type="submit"
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                {editingRate ? 'Update' : 'Add'} Rate Package
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowAddForm(false);
                  setEditingRate(null);
                  setFormData({ packageName: '', hours: 0, price: 0, isActive: true });
                }}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Rates List */}
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Package Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price/Hour</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {rates.map((rate) => (
              <tr key={rate.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{rate.packageName}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{rate.hours}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${rate.price.toFixed(2)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${rate.pricePerHour.toFixed(2)}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleStatus(rate.id)}
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      rate.isActive
                        ? 'bg-emerald-100 text-emerald-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {rate.isActive ? 'Active' : 'Inactive'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    onClick={() => handleEdit(rate)}
                    className="text-emerald-600 hover:text-emerald-900"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(rate.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// Bonus Limits Management Tab
function BonusesTab() {
  const [bonusSettings, setBonusSettings] = useState({
    individual: {
      enabled: true,
      minPurchase: 50,
      bonusPercentage: 10,
      maxBonusAmount: 20,
      description: 'Get 10% bonus hours on purchases over $50'
    },
    corporate: {
      enabled: true,
      minPurchase: 200,
      bonusPercentage: 15,
      maxBonusAmount: 100,
      description: 'Get 15% bonus hours on purchases over $200'
    }
  });

  const [editingType, setEditingType] = useState<'individual' | 'corporate' | null>(null);
  const [formData, setFormData] = useState({
    enabled: true,
    minPurchase: 0,
    bonusPercentage: 0,
    maxBonusAmount: 0,
    description: ''
  });

  const handleEdit = (type: 'individual' | 'corporate') => {
    setEditingType(type);
    setFormData(bonusSettings[type]);
  };

  const handleSave = () => {
    if (editingType) {
      setBonusSettings({
        ...bonusSettings,
        [editingType]: formData
      });
      setEditingType(null);
    }
  };

  const handleCancel = () => {
    setEditingType(null);
    setFormData({
      enabled: true,
      minPurchase: 0,
      bonusPercentage: 0,
      maxBonusAmount: 0,
      description: ''
    });
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-2">Bonus Limits Management</h2>
        <p className="text-sm text-gray-600">Configure bonus hour settings for individual and corporate accounts</p>
      </div>

      <div className="space-y-6">
        {/* Individual Account Bonuses */}
        <div className="bg-emerald-50 border border-emerald-200 rounded-lg p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-medium text-emerald-800">Individual Account Bonuses</h3>
              <p className="text-sm text-emerald-600 mt-1">Bonus settings for individual user accounts</p>
            </div>
            <button
              onClick={() => handleEdit('individual')}
              className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Edit Settings
            </button>
          </div>

          {editingType === 'individual' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={formData.enabled.toString()}
                    onChange={(e) => setFormData({ ...formData, enabled: e.target.value === 'true' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="true">Enabled</option>
                    <option value="false">Disabled</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Min Purchase ($)</label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.minPurchase}
                    onChange={(e) => setFormData({ ...formData, minPurchase: parseFloat(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Bonus Percentage (%)</label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={formData.bonusPercentage}
                    onChange={(e) => setFormData({ ...formData, bonusPercentage: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Max Bonus ($)</label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.maxBonusAmount}
                    onChange={(e) => setFormData({ ...formData, maxBonusAmount: parseFloat(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  rows={2}
                  placeholder="Describe the bonus offer..."
                />
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={handleSave}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Save Changes
                </button>
                <button
                  onClick={handleCancel}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <span className="text-sm font-medium text-gray-500">Status:</span>
                <p className="text-sm text-gray-900">{bonusSettings.individual.enabled ? 'Enabled' : 'Disabled'}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Min Purchase:</span>
                <p className="text-sm text-gray-900">${bonusSettings.individual.minPurchase}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Bonus Percentage:</span>
                <p className="text-sm text-gray-900">{bonusSettings.individual.bonusPercentage}%</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Max Bonus:</span>
                <p className="text-sm text-gray-900">${bonusSettings.individual.maxBonusAmount}</p>
              </div>
              <div className="md:col-span-2 lg:col-span-4">
                <span className="text-sm font-medium text-gray-500">Description:</span>
                <p className="text-sm text-gray-900">{bonusSettings.individual.description}</p>
              </div>
            </div>
          )}
        </div>

        {/* Corporate Account Bonuses */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-medium text-blue-800">Corporate Account Bonuses</h3>
              <p className="text-sm text-blue-600 mt-1">Bonus settings for corporate user accounts</p>
            </div>
            <button
              onClick={() => handleEdit('corporate')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Edit Settings
            </button>
          </div>

          {editingType === 'corporate' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={formData.enabled.toString()}
                    onChange={(e) => setFormData({ ...formData, enabled: e.target.value === 'true' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="true">Enabled</option>
                    <option value="false">Disabled</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Min Purchase ($)</label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.minPurchase}
                    onChange={(e) => setFormData({ ...formData, minPurchase: parseFloat(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Bonus Percentage (%)</label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={formData.bonusPercentage}
                    onChange={(e) => setFormData({ ...formData, bonusPercentage: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Max Bonus ($)</label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.maxBonusAmount}
                    onChange={(e) => setFormData({ ...formData, maxBonusAmount: parseFloat(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  rows={2}
                  placeholder="Describe the bonus offer..."
                />
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={handleSave}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                >
                  Save Changes
                </button>
                <button
                  onClick={handleCancel}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <span className="text-sm font-medium text-gray-500">Status:</span>
                <p className="text-sm text-gray-900">{bonusSettings.corporate.enabled ? 'Enabled' : 'Disabled'}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Min Purchase:</span>
                <p className="text-sm text-gray-900">${bonusSettings.corporate.minPurchase}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Bonus Percentage:</span>
                <p className="text-sm text-gray-900">{bonusSettings.corporate.bonusPercentage}%</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Max Bonus:</span>
                <p className="text-sm text-gray-900">${bonusSettings.corporate.maxBonusAmount}</p>
              </div>
              <div className="md:col-span-2 lg:col-span-4">
                <span className="text-sm font-medium text-gray-500">Description:</span>
                <p className="text-sm text-gray-900">{bonusSettings.corporate.description}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// User Management Tab
function UsersTab() {
  const [users, setUsers] = useState([
    {
      id: 1,
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      clientType: 'Individual',
      companyName: null,
      role: 'user',
      isActive: true,
      createdAt: '2024-01-15',
      hoursBought: 50,
      hoursUsed: 25
    },
    {
      id: 2,
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      clientType: 'Corporate',
      companyName: 'ABC Corp',
      role: 'user',
      isActive: true,
      createdAt: '2024-01-10',
      hoursBought: 200,
      hoursUsed: 150
    },
    {
      id: 3,
      firstName: 'Admin',
      lastName: 'User',
      email: '<EMAIL>',
      clientType: 'Individual',
      companyName: null,
      role: 'admin',
      isActive: true,
      createdAt: '2024-01-01',
      hoursBought: 0,
      hoursUsed: 0
    }
  ]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterRole, setFilterRole] = useState('all');

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user.companyName && user.companyName.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesType = filterType === 'all' || user.clientType.toLowerCase() === filterType.toLowerCase();
    const matchesRole = filterRole === 'all' || user.role === filterRole;

    return matchesSearch && matchesType && matchesRole;
  });

  const toggleUserStatus = (id: number) => {
    if (confirm('Are you sure you want to change this user\'s status?')) {
      setUsers(users.map(user =>
        user.id === id ? { ...user, isActive: !user.isActive } : user
      ));
    }
  };

  const changeUserRole = (id: number, newRole: string) => {
    if (confirm(`Are you sure you want to change this user's role to ${newRole}?`)) {
      setUsers(users.map(user =>
        user.id === id ? { ...user, role: newRole } : user
      ));
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">User Management</h2>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search Users</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
              placeholder="Search by name, email, or company..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Account Type</label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
            >
              <option value="all">All Types</option>
              <option value="individual">Individual</option>
              <option value="corporate">Corporate</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
            >
              <option value="all">All Roles</option>
              <option value="user">User</option>
              <option value="admin">Admin</option>
            </select>
          </div>
          <div className="flex items-end">
            <div className="text-sm text-gray-600">
              Showing {filteredUsers.length} of {users.length} users
            </div>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account Type</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredUsers.map((user) => (
              <tr key={user.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {user.firstName} {user.lastName}
                    </div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                    {user.companyName && (
                      <div className="text-sm text-gray-500">{user.companyName}</div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    user.clientType === 'Corporate'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-emerald-100 text-emerald-800'
                  }`}>
                    {user.clientType}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <select
                    value={user.role}
                    onChange={(e) => changeUserRole(user.id, e.target.value)}
                    className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                  >
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div>Bought: {user.hoursBought}</div>
                  <div>Used: {user.hoursUsed}</div>
                  <div>Available: {user.hoursBought - user.hoursUsed}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button
                    onClick={() => toggleUserStatus(user.id)}
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.isActive
                        ? 'bg-emerald-100 text-emerald-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {user.isActive ? 'Active' : 'Inactive'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(user.createdAt).toLocaleDateString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button className="text-emerald-600 hover:text-emerald-900">
                    View Details
                  </button>
                  <button className="text-blue-600 hover:text-blue-900">
                    Edit Hours
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredUsers.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No users found matching your criteria.</p>
        </div>
      )}
    </div>
  );
}

// System Settings Tab
function SettingsTab() {
  const [settings, setSettings] = useState({
    general: {
      appName: 'Staff Hall',
      supportEmail: '<EMAIL>',
      maintenanceMode: false,
      allowRegistration: true
    },
    notifications: {
      emailNotifications: true,
      jobCompletionNotifications: true,
      paymentNotifications: true,
      systemAlerts: true
    },
    security: {
      sessionTimeout: 30,
      passwordMinLength: 8,
      requireTwoFactor: false,
      maxLoginAttempts: 5
    }
  });

  const [activeSection, setActiveSection] = useState('general');

  const handleSettingChange = (section: string, key: string, value: any) => {
    setSettings({
      ...settings,
      [section]: {
        ...settings[section as keyof typeof settings],
        [key]: value
      }
    });
  };

  const saveSettings = () => {
    // Here you would typically save to your backend
    alert('Settings saved successfully!');
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-lg font-medium text-gray-900 mb-2">System Settings</h2>
        <p className="text-sm text-gray-600">Configure application-wide settings and preferences</p>
      </div>

      {/* Settings Navigation */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          {[
            { id: 'general', name: 'General', icon: '⚙️' },
            { id: 'notifications', name: 'Notifications', icon: '🔔' },
            { id: 'security', name: 'Security', icon: '🔒' }
          ].map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeSection === section.id
                  ? 'bg-emerald-100 text-emerald-700'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <span className="mr-2">{section.icon}</span>
              {section.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Settings Content */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        {activeSection === 'general' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">General Settings</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Application Name</label>
                <input
                  type="text"
                  value={settings.general.appName}
                  onChange={(e) => handleSettingChange('general', 'appName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Support Email</label>
                <input
                  type="email"
                  value={settings.general.supportEmail}
                  onChange={(e) => handleSettingChange('general', 'supportEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="maintenanceMode"
                  checked={settings.general.maintenanceMode}
                  onChange={(e) => handleSettingChange('general', 'maintenanceMode', e.target.checked)}
                  className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
                />
                <label htmlFor="maintenanceMode" className="ml-2 block text-sm text-gray-900">
                  Enable Maintenance Mode
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="allowRegistration"
                  checked={settings.general.allowRegistration}
                  onChange={(e) => handleSettingChange('general', 'allowRegistration', e.target.checked)}
                  className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
                />
                <label htmlFor="allowRegistration" className="ml-2 block text-sm text-gray-900">
                  Allow New User Registration
                </label>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'notifications' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Notification Settings</h3>

            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="emailNotifications"
                  checked={settings.notifications.emailNotifications}
                  onChange={(e) => handleSettingChange('notifications', 'emailNotifications', e.target.checked)}
                  className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
                />
                <label htmlFor="emailNotifications" className="ml-2 block text-sm text-gray-900">
                  Enable Email Notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="jobCompletionNotifications"
                  checked={settings.notifications.jobCompletionNotifications}
                  onChange={(e) => handleSettingChange('notifications', 'jobCompletionNotifications', e.target.checked)}
                  className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
                />
                <label htmlFor="jobCompletionNotifications" className="ml-2 block text-sm text-gray-900">
                  Job Completion Notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="paymentNotifications"
                  checked={settings.notifications.paymentNotifications}
                  onChange={(e) => handleSettingChange('notifications', 'paymentNotifications', e.target.checked)}
                  className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
                />
                <label htmlFor="paymentNotifications" className="ml-2 block text-sm text-gray-900">
                  Payment Notifications
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="systemAlerts"
                  checked={settings.notifications.systemAlerts}
                  onChange={(e) => handleSettingChange('notifications', 'systemAlerts', e.target.checked)}
                  className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
                />
                <label htmlFor="systemAlerts" className="ml-2 block text-sm text-gray-900">
                  System Alerts
                </label>
              </div>
            </div>
          </div>
        )}

        {activeSection === 'security' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Security Settings</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                <input
                  type="number"
                  min="5"
                  max="120"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Password Minimum Length</label>
                <input
                  type="number"
                  min="6"
                  max="20"
                  value={settings.security.passwordMinLength}
                  onChange={(e) => handleSettingChange('security', 'passwordMinLength', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
                <input
                  type="number"
                  min="3"
                  max="10"
                  value={settings.security.maxLoginAttempts}
                  onChange={(e) => handleSettingChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="requireTwoFactor"
                checked={settings.security.requireTwoFactor}
                onChange={(e) => handleSettingChange('security', 'requireTwoFactor', e.target.checked)}
                className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
              />
              <label htmlFor="requireTwoFactor" className="ml-2 block text-sm text-gray-900">
                Require Two-Factor Authentication
              </label>
            </div>
          </div>
        )}

        {/* Save Button */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <button
            onClick={saveSettings}
            className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            Save Settings
          </button>
        </div>
      </div>
    </div>
  );
}
