import axios from 'axios';
import { Job, CreateJobRequest } from '@/types/models';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

export const getJobs = async (): Promise<Job[]> => {
    console.log('getJobs: Starting...');

    // Check if we're in the browser before accessing sessionStorage
    if (typeof window === 'undefined') {
        console.log('getJobs: Not in browser, throwing auth error');
        throw new Error('Authentication required');
    }

    // Add a small delay to ensure sessionStorage is ready
    await new Promise(resolve => setTimeout(resolve, 100));

    const token = sessionStorage.getItem('token');
    const user = sessionStorage.getItem('user');

    console.log('getJobs: Token exists:', !!token);
    console.log('getJobs: User exists:', !!user);
    console.log('getJobs: SessionStorage keys:', Object.keys(sessionStorage));

    if (token) {
        console.log('getJobs: Token preview:', token.substring(0, 20) + '...');
        console.log('getJobs: Token length:', token.length);
    } else {
        console.log('getJobs: Token is null/undefined/empty');
        console.log('getJobs: All sessionStorage items:', {
            token: sessionStorage.getItem('token'),
            user: sessionStorage.getItem('user')
        });
    }

    if (!token) {
        console.log('getJobs: No token found, throwing auth error');
        throw new Error('Authentication required');
    }

    console.log('getJobs: Making API request to:', `${API_URL}/api/jobs`);

    try {
        const response = await axios.get(`${API_URL}/api/jobs`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('getJobs: Response status:', response.status);
        console.log('getJobs: Response data type:', typeof response.data);
        console.log('getJobs: Response data length:', Array.isArray(response.data) ? response.data.length : 'Not an array');
        console.log('getJobs: Response data:', response.data);
        return response.data;
    } catch (error) {
        console.error('getJobs: API request failed:', error);
        if (axios.isAxiosError(error)) {
            console.error('getJobs: Axios error details:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data
            });

            if (error.response?.status === 401) {
                throw new Error('Authentication required');
            }
        }
        throw error;
    }
};

export const getJobById = async (id: number): Promise<Job> => {
    console.log('getJobById: Starting for job ID:', id);

    // Check if we're in the browser before accessing sessionStorage
    if (typeof window === 'undefined') {
        console.log('getJobById: Not in browser, throwing auth error');
        throw new Error('Authentication required');
    }

    const token = sessionStorage.getItem('token');
    if (!token) {
        console.log('getJobById: No token found');
        throw new Error('Authentication required');
    }

    try {
        console.log('getJobById: Making API request to:', `${API_URL}/api/jobs/${id}`);
        console.log('getJobById: Using token:', token.substring(0, 20) + '...');

        const response = await axios.get(`${API_URL}/api/jobs/${id}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log('getJobById: Response status:', response.status);
        console.log('getJobById: Response data:', response.data);
        return response.data;
    } catch (error) {
        console.error('getJobById: API request failed:', error);
        if (axios.isAxiosError(error)) {
            console.error('getJobById: Axios error details:', {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data
            });

            if (error.response?.status === 401) {
                throw new Error('Authentication required');
            }
            if (error.response?.status === 404) {
                throw new Error('Job not found');
            }
            if (error.response?.status === 403) {
                throw new Error('Access denied');
            }
        }
        throw error;
    }
};

export const createJob = async (jobData: CreateJobRequest): Promise<Job> => {
    // Check if we're in the browser before accessing sessionStorage
    if (typeof window === 'undefined') {
        throw new Error('Authentication required');
    }

    const token = sessionStorage.getItem('token');

    if (!token) {
        throw new Error('Authentication required');
    }

    const response = await axios.post(`${API_URL}/api/jobs`, jobData, {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });

    return response.data;
};